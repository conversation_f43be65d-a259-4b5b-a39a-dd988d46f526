# DerLg Implementation Guidelines
## Complete Development Guide for Cambodia Tour Booking Platform

### 📋 **Document Overview**

This comprehensive guideline provides step-by-step implementation instructions for the DerLg Cambodia tour booking platform. It consolidates all project requirements, design specifications, and technical details into actionable development steps.

---

## 🎯 **Project Summary**

**DerLg** is a full-stack Cambodia tour booking platform connecting international tourists with verified local guides, transportation, accommodation, and cultural experiences. The platform serves four user roles: Tourists, Guides, Servers (hotel/transport providers), and Admins.

### **Key Features**
- Multi-role authentication system
- AI-powered tour recommendations
- Real-time GPS tracking for transportation
- Hotel and accommodation booking
- Cultural events and activities
- Multi-language support (English, Khmer, Chinese)
- Comprehensive payment processing
- Admin dashboard and analytics

---

## 🏗️ **Architecture Overview**

### **Technology Stack**
```
Frontend: React + Next.js 14 + TypeScript + Tailwind CSS
Backend: Node.js + Express.js + JWT Authentication
Database: MySQL (localhost:3036, database: derlgDB)
Maps: Google Maps API integration
Real-time: GPS tracking, WebSocket capabilities
AI: OpenAI API for recommendations
Payment: Stripe, PayPal, Bakong integration
```

### **Project Structure**
```
DerLg/
├── Backend/
│   ├── server.ts
│   ├── src/
│   │   ├── controllers/
│   │   ├── models/
│   │   ├── routes/
│   │   ├── middleware/
│   │   └── utils/
│   └── .env
├── Frontend/
│   ├── src/
│   │   ├── app/
│   │   ├── components/
│   │   ├── contexts/
│   │   └── lib/
│   └── package.json
├── mobile_app/
│   └── lib/
└── document/
    ├── requirements.md
    ├── design.md
    ├── tasks.md
    └── proposal.md
```

---

## 🚀 **Phase-by-Phase Implementation Guide**

### **Phase 1: Foundation Setup** ✅ **COMPLETED**

#### 1.1 Project Structure Creation
- [x] Backend folder structure with Express.js
- [x] Frontend setup with React + Next.js + TypeScript
- [x] Mobile app initialization with Flutter
- [x] Environment configuration (.env file)
- [x] Package.json dependencies

#### 1.2 Environment Configuration
```env
# Database Configuration
DB_HOST=localhost
DB_PORT=3036
DB_NAME=derlgDB
DB_USER=root
DB_PASSWORD=12345

# JWT Configuration
JWT_SECRET=derlg_super_secret_jwt_key_2024_cambodia_tours
JWT_REFRESH_SECRET=derlg_refresh_secret_key_2024_secure
JWT_EXPIRE=15m
JWT_REFRESH_EXPIRE=7d

# External APIs
STRIPE_SECRET_KEY=sk_test_placeholder
OPENAI_API_KEY=placeholder_openai_key
GOOGLE_MAPS_API_KEY=your_google_maps_key
```

### **Phase 2: Database Implementation** 🔄 **IN PROGRESS**

#### 2.1 Database Connection Setup
```javascript
// Backend/src/config/database.js
const mysql = require('mysql2/promise');

const pool = mysql.createPool({
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
});

module.exports = pool;
```

#### 2.2 Core Database Schema
```sql
-- Users table with multi-role support
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    profile_image VARCHAR(255),
    user_type ENUM('tourist', 'guide', 'admin', 'server') DEFAULT 'tourist',
    preferred_language ENUM('en', 'km', 'zh') DEFAULT 'en',
    is_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tours table
CREATE TABLE tours (
    id INT PRIMARY KEY AUTO_INCREMENT,
    guide_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    duration_hours INT NOT NULL,
    max_participants INT NOT NULL,
    price_usd DECIMAL(10,2) NOT NULL,
    location VARCHAR(255) NOT NULL,
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    category ENUM('cultural', 'adventure', 'nature', 'food', 'historical', 'religious', 'shopping') NOT NULL,
    difficulty_level ENUM('easy', 'moderate', 'challenging') DEFAULT 'easy',
    includes TEXT,
    excludes TEXT,
    requirements TEXT,
    cancellation_policy TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    average_rating DECIMAL(3,2) DEFAULT 0,
    total_reviews INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (guide_id) REFERENCES users(id)
);

-- Additional tables: bookings, reviews, hotels, transportation, events
-- (Complete schema available in design.md)
```

### **Phase 3: Authentication System** ⏳ **NEXT PRIORITY**

#### 3.1 JWT Middleware Implementation
```javascript
// Backend/src/middleware/auth.js
const jwt = require('jsonwebtoken');

const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }

  jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
    if (err) return res.status(403).json({ error: 'Invalid token' });
    req.user = user;
    next();
  });
};

const authorizeRoles = (...roles) => {
  return (req, res, next) => {
    if (!roles.includes(req.user.user_type)) {
      return res.status(403).json({ error: 'Insufficient permissions' });
    }
    next();
  };
};
```

#### 3.2 User Registration & Login
```javascript
// Backend/src/controllers/authController.js
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');

const register = async (req, res) => {
  try {
    const { email, password, first_name, last_name, user_type } = req.body;
    
    // Hash password
    const password_hash = await bcrypt.hash(password, 12);
    
    // Insert user
    const [result] = await pool.execute(
      'INSERT INTO users (email, password_hash, first_name, last_name, user_type) VALUES (?, ?, ?, ?, ?)',
      [email, password_hash, first_name, last_name, user_type]
    );
    
    // Generate tokens
    const tokens = generateTokens({ id: result.insertId, email, user_type });
    
    res.status(201).json({ message: 'User created successfully', tokens });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};
```

### **Phase 4: Core API Development** ⏳ **PRIORITY**

#### 4.1 Tour Management API
```javascript
// Backend/src/routes/tours.js
router.get('/tours', async (req, res) => {
  const { category, location, price_min, price_max, page = 1 } = req.query;
  // Implement search and filtering logic
});

router.post('/tours', authenticateToken, authorizeRoles('guide'), async (req, res) => {
  // Create new tour (guide only)
});

router.get('/tours/:id', async (req, res) => {
  // Get tour details with guide info and reviews
});
```

#### 4.2 Booking System API
```javascript
// Backend/src/routes/bookings.js
router.post('/bookings', authenticateToken, async (req, res) => {
  // Create booking with validation
  // Check availability
  // Process payment
  // Send confirmation
});

router.get('/bookings/user/:userId', authenticateToken, async (req, res) => {
  // Get user's booking history
});
```

### **Phase 5: Frontend Development** ⏳ **PRIORITY**

#### 5.1 Authentication Context
```typescript
// Frontend/src/contexts/AuthContext.tsx
interface AuthContextType {
  user: User | null;
  login: (email: string, password: string) => Promise<void>;
  register: (userData: RegisterData) => Promise<void>;
  logout: () => void;
  loading: boolean;
}

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Implementation with JWT token management
};
```

#### 5.2 Tour Discovery Components
```typescript
// Frontend/src/components/TourCard.tsx
interface TourCardProps {
  tour: Tour;
  onBook: (tourId: number) => void;
}

export const TourCard: React.FC<TourCardProps> = ({ tour, onBook }) => {
  return (
    <div className="border rounded-lg p-4 shadow-md">
      <h3 className="text-lg font-semibold">{tour.title}</h3>
      <p className="text-gray-600">{tour.description}</p>
      <div className="flex justify-between items-center mt-4">
        <span className="text-xl font-bold">${tour.price_usd}</span>
        <button 
          onClick={() => onBook(tour.id)}
          className="bg-blue-500 text-white px-4 py-2 rounded"
        >
          Book Now
        </button>
      </div>
    </div>
  );
};
```

### **Phase 6: Advanced Features Implementation**

#### 6.1 Real-time GPS Tracking
```javascript
// Backend/src/routes/tracking.js
router.post('/tracking/update', authenticateToken, async (req, res) => {
  const { booking_id, latitude, longitude } = req.body;
  
  await pool.execute(
    'INSERT INTO location_tracking (booking_id, latitude, longitude, timestamp) VALUES (?, ?, ?, NOW())',
    [booking_id, latitude, longitude]
  );
  
  // Emit real-time update via WebSocket
  io.emit(`tracking_${booking_id}`, { latitude, longitude, timestamp: new Date() });
});
```

#### 6.2 Google Maps Integration
```typescript
// Frontend/src/components/MapComponent.tsx
import { GoogleMap, LoadScript, Marker } from '@react-google-maps/api';

export const TrackingMap: React.FC<{ bookingId: string }> = ({ bookingId }) => {
  const [location, setLocation] = useState<{ lat: number; lng: number } | null>(null);
  
  useEffect(() => {
    // WebSocket connection for real-time updates
    const socket = io();
    socket.on(`tracking_${bookingId}`, (data) => {
      setLocation({ lat: data.latitude, lng: data.longitude });
    });
    
    return () => socket.disconnect();
  }, [bookingId]);
  
  return (
    <LoadScript googleMapsApiKey={process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}>
      <GoogleMap zoom={15} center={location}>
        {location && <Marker position={location} />}
      </GoogleMap>
    </LoadScript>
  );
};
```

#### 6.3 AI Travel Assistant
```javascript
// Backend/src/routes/ai.js
router.post('/ai/recommendations', authenticateToken, async (req, res) => {
  const { budget, interests, travel_dates } = req.body;
  
  const prompt = `Generate 3 personalized Cambodia tour recommendations for:
    Budget: $${budget}
    Interests: ${interests.join(', ')}
    Travel dates: ${travel_dates}
    
    Include pricing breakdown and cultural significance.`;
  
  const response = await openai.chat.completions.create({
    model: "gpt-3.5-turbo",
    messages: [{ role: "user", content: prompt }],
    stream: true
  });
  
  // Stream response back to client
  for await (const chunk of response) {
    res.write(chunk.choices[0]?.delta?.content || '');
  }
  res.end();
});
```

---

## 🔐 **Security Implementation Guidelines**

### **Security Checklist**
- [x] JWT authentication with refresh tokens
- [x] Password hashing with bcrypt (12 rounds)
- [ ] Input validation and sanitization
- [ ] SQL injection prevention (parameterized queries)
- [ ] XSS protection with helmet.js
- [ ] Rate limiting implementation
- [ ] HTTPS enforcement
- [ ] CORS configuration

### **Security Implementation**
```javascript
// Backend/src/middleware/security.js
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100 // limit each IP to 100 requests per windowMs
});

// Input validation
const validateInput = (schema) => {
  return (req, res, next) => {
    const { error } = schema.validate(req.body);
    if (error) {
      return res.status(400).json({ error: error.details[0].message });
    }
    next();
  };
};
```

---

## 📱 **Mobile App Development Guidelines**

### **Flutter Implementation Priority**
1. **Core Features**: Authentication, tour browsing, booking management
2. **Real-time Features**: GPS tracking, push notifications
3. **Offline Capabilities**: Cached bookings, offline maps
4. **Camera Integration**: Photo sharing, QR code scanning

### **Flutter Structure**
```dart
// mobile_app/lib/main.dart
import 'package:flutter/material.dart';

void main() {
  runApp(DerLgApp());
}

class DerLgApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'DerLg - Cambodia Tours',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        fontFamily: 'Roboto',
      ),
      home: SplashScreen(),
      routes: {
        '/login': (context) => LoginScreen(),
        '/home': (context) => HomeScreen(),
        '/tours': (context) => ToursScreen(),
        '/bookings': (context) => BookingsScreen(),
      },
    );
  }
}
```

---

## 🌍 **Multi-language Implementation**

### **i18n Setup**
```typescript
// Frontend/src/lib/i18n.ts
export const translations = {
  en: {
    'tour.book_now': 'Book Now',
    'tour.price': 'Price',
    'booking.confirm': 'Confirm Booking'
  },
  km: {
    'tour.book_now': 'ការកក់ឥឡូវនេះ',
    'tour.price': 'តម្លៃ',
    'booking.confirm': 'បញ្ជាក់ការកក់'
  },
  zh: {
    'tour.book_now': '立即预订',
    'tour.price': '价格',
    'booking.confirm': '确认预订'
  }
};

export const useTranslation = () => {
  const { language } = useLanguage();
  
  const t = (key: string) => {
    return translations[language][key] || key;
  };
  
  return { t };
};
```

---

## 💳 **Payment Integration Guidelines**

### **Stripe Integration**
```javascript
// Backend/src/services/paymentService.js
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);

const createPaymentIntent = async (amount, currency = 'usd') => {
  const paymentIntent = await stripe.paymentIntents.create({
    amount: amount * 100, // Convert to cents
    currency,
    automatic_payment_methods: {
      enabled: true,
    },
  });
  
  return paymentIntent.client_secret;
};
```

### **Frontend Payment Component**
```typescript
// Frontend/src/components/PaymentForm.tsx
import { loadStripe } from '@stripe/stripe-js';
import { Elements, CardElement, useStripe, useElements } from '@stripe/react-stripe-js';

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY);

export const PaymentForm: React.FC = () => {
  const stripe = useStripe();
  const elements = useElements();
  
  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    
    if (!stripe || !elements) return;
    
    const { error, paymentIntent } = await stripe.confirmCardPayment(clientSecret, {
      payment_method: {
        card: elements.getElement(CardElement)!,
      }
    });
    
    if (error) {
      console.error('Payment failed:', error);
    } else {
      console.log('Payment succeeded:', paymentIntent);
    }
  };
  
  return (
    <form onSubmit={handleSubmit}>
      <CardElement />
      <button type="submit" disabled={!stripe}>
        Pay Now
      </button>
    </form>
  );
};
```

---

## 📊 **Analytics & Monitoring Guidelines**

### **Key Metrics to Track**
1. **User Metrics**: Registration, login frequency, role distribution
2. **Booking Metrics**: Conversion rates, popular tours, revenue
3. **Performance Metrics**: API response times, error rates
4. **Safety Metrics**: Emergency incidents, guide ratings

### **Analytics Implementation**
```javascript
// Backend/src/middleware/analytics.js
const logEvent = async (event_type, user_id, data) => {
  await pool.execute(
    'INSERT INTO analytics_events (event_type, user_id, data, timestamp) VALUES (?, ?, ?, NOW())',
    [event_type, user_id, JSON.stringify(data)]
  );
};

// Usage examples
logEvent('tour_viewed', user_id, { tour_id, category });
logEvent('booking_created', user_id, { tour_id, amount, payment_method });
logEvent('payment_completed', user_id, { booking_id, amount });
```

---

## 🧪 **Testing Strategy**

### **Testing Framework Setup**
```javascript
// Backend/tests/auth.test.js
const request = require('supertest');
const app = require('../src/app');

describe('Authentication', () => {
  test('Should register a new user', async () => {
    const response = await request(app)
      .post('/api/auth/register')
      .send({
        email: '<EMAIL>',
        password: 'password123',
        first_name: 'Test',
        last_name: 'User',
        user_type: 'tourist'
      });
      
    expect(response.status).toBe(201);
    expect(response.body.tokens).toBeDefined();
  });
});
```

### **Frontend Testing**
```typescript
// Frontend/src/components/__tests__/TourCard.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { TourCard } from '../TourCard';

test('should display tour information correctly', () => {
  const mockTour = {
    id: 1,
    title: 'Angkor Wat Tour',
    description: 'Historic temple complex',
    price_usd: 50
  };
  
  render(<TourCard tour={mockTour} onBook={jest.fn()} />);
  
  expect(screen.getByText('Angkor Wat Tour')).toBeInTheDocument();
  expect(screen.getByText('$50')).toBeInTheDocument();
});
```

---

## 🚀 **Deployment Guidelines**

### **Production Environment Setup**
```bash
# Server setup commands
sudo apt update
sudo apt install nodejs npm mysql-server nginx

# SSL Certificate with Let's Encrypt
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com

# PM2 for process management
npm install -g pm2
pm2 start server.js --name "derlg-backend"
pm2 startup
pm2 save
```

### **Environment Configuration**
```env
# Production .env
NODE_ENV=production
PORT=5000
DB_HOST=your-production-db-host
STRIPE_SECRET_KEY=sk_live_your_real_stripe_key
GOOGLE_MAPS_API_KEY=your_production_google_maps_key
```

### **Nginx Configuration**
```nginx
# /etc/nginx/sites-available/derlg
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    location /api {
        proxy_pass http://localhost:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

---

## 📋 **Implementation Checklist**

### **Backend Development**
- [ ] Database schema creation and migration scripts
- [ ] Authentication system with JWT
- [ ] User management API endpoints
- [ ] Tour management CRUD operations
- [ ] Booking system with payment integration
- [ ] Real-time GPS tracking implementation
- [ ] Review and rating system
- [ ] Admin dashboard API
- [ ] AI recommendation engine integration
- [ ] Multi-language API support

### **Frontend Development**
- [ ] Authentication UI and context
- [ ] Tour discovery and search interface
- [ ] Booking flow and payment forms
- [ ] User dashboard for all roles
- [ ] Google Maps integration
- [ ] Real-time tracking components
- [ ] Admin panel interface
- [ ] Mobile-responsive design
- [ ] Multi-language switching
- [ ] AI chat interface

### **Mobile App Development**
- [ ] Flutter app setup and structure
- [ ] API integration and state management
- [ ] Offline capabilities
- [ ] Push notifications
- [ ] Camera and photo sharing
- [ ] GPS and navigation features

### **Testing & Quality Assurance**
- [ ] Unit tests for API endpoints
- [ ] Integration tests for critical flows
- [ ] Frontend component testing
- [ ] End-to-end testing with Cypress
- [ ] Performance testing and optimization
- [ ] Security testing and penetration testing

### **Deployment & Operations**
- [ ] Production server setup
- [ ] Database deployment and backup
- [ ] SSL certificate installation
- [ ] CI/CD pipeline configuration
- [ ] Monitoring and logging setup
- [ ] Performance monitoring
- [ ] Error tracking and alerting

---

## 🎯 **Success Criteria**

### **Technical Metrics**
- API response time < 500ms
- 99.9% uptime
- Zero critical security vulnerabilities
- Mobile app rating > 4.5 stars

### **Business Metrics**
- 1000+ registered users in first 3 months
- 100+ verified guides onboarded
- 500+ successful bookings completed
- Average booking value > $75

### **User Experience Metrics**
- User registration completion rate > 80%
- Booking completion rate > 70%
- Average guide rating > 4.2/5
- Customer support response time < 2 hours

---

## 📞 **Support & Maintenance**

### **Monitoring Setup**
- Server performance monitoring
- Database query optimization
- API endpoint monitoring
- User behavior analytics
- Error tracking and logging

### **Maintenance Schedule**
- Daily: Monitor system health and user activity
- Weekly: Review user feedback and support tickets
- Monthly: Performance optimization and security updates
- Quarterly: Feature updates and platform enhancements

---

## 🏆 **Conclusion**

This implementation guideline provides a comprehensive roadmap for building the DerLg Cambodia tour booking platform. Follow the phase-by-phase approach, prioritize security and user experience, and maintain focus on the core value proposition of connecting tourists with authentic Cambodian experiences.

The platform has strong foundation with clear requirements, detailed technical specifications, and a well-defined business model. Success depends on careful execution of each phase, thorough testing, and continuous iteration based on user feedback.

**Next Immediate Actions:**
1. Complete database schema implementation
2. Build authentication system
3. Develop core API endpoints
4. Create frontend authentication flow
5. Implement tour discovery features

---

*Document Version: 1.0*  
*Last Updated: August 20, 2025*  
*Author: DerLg Development Team*
