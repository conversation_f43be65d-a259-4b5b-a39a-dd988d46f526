# Design Document

## Overview

DerLg is a full-stack web and mobile platform built with React.js/Next.js frontend and Node.js backend, designed to connect tourists with verified local tour guides in Cambodia. The system features AI-powered recommendations, multi-language support, integrated payment processing, and comprehensive booking management.

## Architecture

### System Architecture
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend API   │    │   Database      │
│   React/Next.js │◄──►│   Node.js       │◄──►│   MySQL         │
│   Mobile App    │    │   Express.js    │    │   mockUp_derlg  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                    ┌─────────────────┐
                    │  External APIs  │
                    │  - Payment      │
                    │  - Weather      │
                    │  - Translation  │
                    │  - Maps         │
                    └─────────────────┘
```

### Technology Stack
- **Frontend**: React.js, Next.js for SSR/SSG
- **Backend**: Node.js with Express.js framework
- **Database**: MySQL (mockUp_derlg)
- **Authentication**: JWT tokens with refresh token strategy
- **Payment**: Stripe (Credit Cards), PayPal API, Bakong integration
- **File Storage**: Local storage with option for cloud migration
- **Real-time**: Socket.io for notifications
- **AI/ML**: OpenAI API for recommendations, Google Translate API

## Components and Interfaces

### Frontend Components

#### Core Pages
- **Landing Page**: Hero section, featured tours, search functionality
- **Search Results**: Filtered tour listings with map integration
- **Tour Details**: Comprehensive tour information, booking interface
- **User Dashboard**: Booking management, profile, preferences
- **Guide Dashboard**: Tour management, earnings, calendar
- **Admin Panel**: User management, content moderation, analytics

#### Reusable Components
- **TourCard**: Display tour information with ratings and pricing
- **SearchFilters**: Advanced filtering by price, location, date, type
- **BookingForm**: Multi-step booking process with validation
- **ReviewSystem**: Rating and review display/submission
- **PaymentGateway**: Integrated payment processing
- **ChatWidget**: Real-time communication between users and guides

### Backend API Structure

#### Authentication Endpoints
```
POST /api/auth/register
POST /api/auth/login
POST /api/auth/refresh
POST /api/auth/logout
POST /api/auth/forgot-password
POST /api/auth/reset-password
```

#### User Management
```
GET /api/users/profile
PUT /api/users/profile
GET /api/users/bookings
DELETE /api/users/account
```

#### Tour Management
```
GET /api/tours
GET /api/tours/:id
POST /api/tours (guide only)
PUT /api/tours/:id (guide only)
DELETE /api/tours/:id (guide only)
GET /api/tours/search
```

#### Booking System
```
POST /api/bookings
GET /api/bookings/:id
PUT /api/bookings/:id/cancel
GET /api/bookings/user/:userId
GET /api/bookings/guide/:guideId
```

#### AI Recommendations
```
POST /api/ai/recommendations
POST /api/ai/custom-itinerary
GET /api/ai/trending-destinations
```

## Data Models

### User Model
```sql
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    profile_image VARCHAR(255),
    user_type ENUM('tourist', 'guide', 'admin') DEFAULT 'tourist',
    preferred_language ENUM('en', 'km', 'zh') DEFAULT 'en',
    is_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### Tour Model
```sql
CREATE TABLE tours (
    id INT PRIMARY KEY AUTO_INCREMENT,
    guide_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    duration_hours INT NOT NULL,
    max_participants INT NOT NULL,
    price_usd DECIMAL(10,2) NOT NULL,
    location VARCHAR(255) NOT NULL,
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    category ENUM('cultural', 'adventure', 'nature', 'food', 'historical') NOT NULL,
    difficulty_level ENUM('easy', 'moderate', 'challenging') DEFAULT 'easy',
    includes TEXT,
    excludes TEXT,
    requirements TEXT,
    cancellation_policy TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    average_rating DECIMAL(3,2) DEFAULT 0,
    total_reviews INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (guide_id) REFERENCES users(id)
);
```

### Booking Model
```sql
CREATE TABLE bookings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    tour_id INT NOT NULL,
    booking_date DATE NOT NULL,
    participants INT NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    status ENUM('pending', 'confirmed', 'cancelled', 'completed') DEFAULT 'pending',
    payment_status ENUM('pending', 'paid', 'refunded') DEFAULT 'pending',
    payment_method VARCHAR(50),
    payment_id VARCHAR(255),
    special_requests TEXT,
    emergency_contact_name VARCHAR(100),
    emergency_contact_phone VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (tour_id) REFERENCES tours(id)
);
```

### Review Model
```sql
CREATE TABLE reviews (
    id INT PRIMARY KEY AUTO_INCREMENT,
    booking_id INT NOT NULL,
    user_id INT NOT NULL,
    tour_id INT NOT NULL,
    guide_id INT NOT NULL,
    rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
    comment TEXT,
    is_verified BOOLEAN DEFAULT TRUE,
    is_moderated BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (booking_id) REFERENCES bookings(id),
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (tour_id) REFERENCES tours(id),
    FOREIGN KEY (guide_id) REFERENCES users(id)
);
```

### Hotel Model
```sql
CREATE TABLE hotels (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    address VARCHAR(255) NOT NULL,
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    star_rating INT CHECK (star_rating >= 1 AND star_rating <= 5),
    price_per_night DECIMAL(10,2) NOT NULL,
    amenities JSON,
    images JSON,
    contact_phone VARCHAR(20),
    contact_email VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    average_rating DECIMAL(3,2) DEFAULT 0,
    total_reviews INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## Error Handling

### API Error Response Format
```json
{
    "success": false,
    "error": {
        "code": "VALIDATION_ERROR",
        "message": "Invalid input data",
        "details": {
            "field": "email",
            "reason": "Invalid email format"
        }
    },
    "timestamp": "2024-01-15T10:30:00Z"
}
```

### Error Categories
- **Authentication Errors**: Invalid credentials, expired tokens
- **Validation Errors**: Invalid input data, missing required fields
- **Authorization Errors**: Insufficient permissions
- **Business Logic Errors**: Booking conflicts, payment failures
- **External API Errors**: Payment gateway, weather service failures
- **System Errors**: Database connection, server errors

### Error Handling Strategy
- Centralized error handling middleware
- Structured error logging with Winston
- User-friendly error messages in multiple languages
- Automatic retry mechanisms for transient failures
- Graceful degradation for non-critical features

## Testing Strategy

### Unit Testing
- **Backend**: Jest for API endpoints, business logic, and utilities
- **Frontend**: React Testing Library for components
- **Database**: Test database with seed data for consistent testing
- **Coverage Target**: Minimum 80% code coverage

### Integration Testing
- API endpoint testing with Supertest
- Database integration tests
- Payment gateway integration tests
- External API mocking and testing

### End-to-End Testing
- Cypress for critical user journeys
- Automated booking flow testing
- Payment processing validation
- Multi-language functionality testing

### Performance Testing
- Load testing with Artillery.js
- Database query optimization
- API response time monitoring
- Frontend bundle size optimization

### Security Testing
- JWT token validation and expiration
- SQL injection prevention
- XSS protection
- Rate limiting implementation
- Input sanitization and validation

## Security Considerations

### Authentication & Authorization
- JWT tokens with short expiration (15 minutes)
- Refresh tokens with longer expiration (7 days)
- Role-based access control (RBAC)
- Password hashing with bcrypt (12 rounds)
- Account lockout after failed attempts

### Data Protection
- Input validation and sanitization
- SQL injection prevention with parameterized queries
- XSS protection with Content Security Policy
- HTTPS enforcement
- Sensitive data encryption at rest

### API Security
- Rate limiting (100 requests per minute per IP)
- CORS configuration
- Request size limits
- API key authentication for external services
- Audit logging for sensitive operations

## Deployment Architecture

### Development Environment
- Local MySQL database (mockUp_derlg)
- Node.js development server
- React development server with hot reload
- Environment variables for configuration

### Production Considerations
- Database connection pooling
- Redis for session storage and caching
- Load balancing for high availability
- CDN for static assets
- Monitoring and logging infrastructure
- Automated backup strategies