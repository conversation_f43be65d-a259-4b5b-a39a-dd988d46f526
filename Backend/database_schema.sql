-- DerLg Cambodia Tour Booking Platform Database Schema
-- MySQL Database: derlgDB
-- Created: August 2025
-- Description: Comprehensive schema for multi-role tour booking platform

-- Create database if not exists
CREATE DATABASE IF NOT EXISTS derlgDB;
USE derlgDB;

-- Drop tables if they exist (for clean setup)
DROP TABLE IF EXISTS location_tracking;
DROP TABLE IF EXISTS hotel_bookings;
DROP TABLE IF EXISTS transportation_bookings;
DROP TABLE IF EXISTS reviews;
DROP TABLE IF EXISTS bookings;
DROP TABLE IF EXISTS tour_images;
DROP TABLE IF EXISTS tours;
DROP TABLE IF EXISTS events;
DROP TABLE IF EXISTS hotels;
DROP TABLE IF EXISTS transportation;
DROP TABLE IF EXISTS users;

-- Users table with multi-role support
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    last_name VA<PERSON>HAR(100) NOT NULL,
    phone VARCHAR(20),
    profile_image VARCHAR(255),
    user_type <PERSON><PERSON><PERSON>('tourist', 'guide', 'admin', 'server') DEFAULT 'tourist',
    preferred_language ENUM('en', 'km', 'zh') DEFAULT 'en',
    is_verified BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    emergency_contact_name VARCHAR(100),
    emergency_contact_phone VARCHAR(20),
    bio TEXT,
    specializations JSON, -- For guides: ["cultural", "adventure", "nature"]
    languages_spoken JSON, -- ["en", "km", "zh", "fr"]
    verification_documents JSON, -- Document URLs and status
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_email (email),
    INDEX idx_user_type (user_type),
    INDEX idx_is_verified (is_verified),
    INDEX idx_is_active (is_active)
);

-- Tours table
CREATE TABLE tours (
    id INT PRIMARY KEY AUTO_INCREMENT,
    guide_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    duration_hours INT NOT NULL,
    max_participants INT NOT NULL,
    price_usd DECIMAL(10,2) NOT NULL,
    location VARCHAR(255) NOT NULL,
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    category ENUM('cultural', 'adventure', 'nature', 'food', 'historical', 'religious', 'shopping') NOT NULL,
    difficulty_level ENUM('easy', 'moderate', 'challenging') DEFAULT 'easy',
    includes TEXT, -- What's included in the tour
    excludes TEXT, -- What's not included
    requirements TEXT, -- Age, fitness, equipment requirements
    cancellation_policy TEXT,
    meeting_point VARCHAR(255),
    meeting_point_lat DECIMAL(10,8),
    meeting_point_lng DECIMAL(11,8),
    is_active BOOLEAN DEFAULT TRUE,
    average_rating DECIMAL(3,2) DEFAULT 0,
    total_reviews INT DEFAULT 0,
    total_bookings INT DEFAULT 0,
    seasonal_availability JSON, -- {"months": [1,2,3,4,5,6,7,8,9,10,11,12], "weather_dependent": true}
    cultural_significance TEXT,
    dress_code TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (guide_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_guide_id (guide_id),
    INDEX idx_category (category),
    INDEX idx_location (location),
    INDEX idx_price (price_usd),
    INDEX idx_is_active (is_active),
    INDEX idx_average_rating (average_rating),
    FULLTEXT idx_search (title, description, location)
);

-- Tour images table
CREATE TABLE tour_images (
    id INT PRIMARY KEY AUTO_INCREMENT,
    tour_id INT NOT NULL,
    image_url VARCHAR(255) NOT NULL,
    caption VARCHAR(255),
    is_primary BOOLEAN DEFAULT FALSE,
    display_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (tour_id) REFERENCES tours(id) ON DELETE CASCADE,
    INDEX idx_tour_id (tour_id),
    INDEX idx_is_primary (is_primary)
);

-- Bookings table
CREATE TABLE bookings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    tour_id INT NOT NULL,
    booking_date DATE NOT NULL,
    booking_time TIME,
    participants INT NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    status ENUM('pending', 'confirmed', 'cancelled', 'completed', 'refunded') DEFAULT 'pending',
    payment_status ENUM('pending', 'paid', 'failed', 'refunded') DEFAULT 'pending',
    payment_method ENUM('stripe', 'paypal', 'bakong') DEFAULT 'stripe',
    payment_id VARCHAR(255),
    payment_intent_id VARCHAR(255),
    special_requests TEXT,
    emergency_contact_name VARCHAR(100),
    emergency_contact_phone VARCHAR(20),
    cancellation_reason TEXT,
    refund_amount DECIMAL(10,2) DEFAULT 0,
    guide_notes TEXT,
    tourist_notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (tour_id) REFERENCES tours(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_tour_id (tour_id),
    INDEX idx_booking_date (booking_date),
    INDEX idx_status (status),
    INDEX idx_payment_status (payment_status)
);

-- Reviews table
CREATE TABLE reviews (
    id INT PRIMARY KEY AUTO_INCREMENT,
    booking_id INT NOT NULL,
    user_id INT NOT NULL,
    tour_id INT NOT NULL,
    guide_id INT NOT NULL,
    rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
    comment TEXT,
    guide_rating INT CHECK (guide_rating >= 1 AND guide_rating <= 5),
    tour_rating INT CHECK (tour_rating >= 1 AND tour_rating <= 5),
    value_rating INT CHECK (value_rating >= 1 AND value_rating <= 5),
    safety_rating INT CHECK (safety_rating >= 1 AND safety_rating <= 5),
    images JSON, -- Array of image URLs
    is_verified BOOLEAN DEFAULT TRUE,
    is_moderated BOOLEAN DEFAULT FALSE,
    moderation_notes TEXT,
    helpful_votes INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (tour_id) REFERENCES tours(id) ON DELETE CASCADE,
    FOREIGN KEY (guide_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_booking_id (booking_id),
    INDEX idx_user_id (user_id),
    INDEX idx_tour_id (tour_id),
    INDEX idx_guide_id (guide_id),
    INDEX idx_rating (rating),
    INDEX idx_is_verified (is_verified),
    UNIQUE KEY unique_booking_review (booking_id, user_id)
);

-- Hotels table
CREATE TABLE hotels (
    id INT PRIMARY KEY AUTO_INCREMENT,
    owner_id INT, -- Server user who owns this hotel
    name VARCHAR(255) NOT NULL,
    description TEXT,
    address VARCHAR(255) NOT NULL,
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    star_rating INT CHECK (star_rating >= 1 AND star_rating <= 5),
    price_per_night DECIMAL(10,2) NOT NULL,
    amenities JSON, -- ["wifi", "pool", "restaurant", "spa", "gym"]
    room_types JSON, -- [{"type": "single", "price": 50, "available": 10}]
    images JSON, -- Array of image URLs
    contact_phone VARCHAR(20),
    contact_email VARCHAR(255),
    check_in_time TIME DEFAULT '14:00:00',
    check_out_time TIME DEFAULT '11:00:00',
    cancellation_policy TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    average_rating DECIMAL(3,2) DEFAULT 0,
    total_reviews INT DEFAULT 0,
    total_bookings INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_owner_id (owner_id),
    INDEX idx_location (latitude, longitude),
    INDEX idx_price (price_per_night),
    INDEX idx_star_rating (star_rating),
    INDEX idx_is_active (is_active),
    FULLTEXT idx_search (name, description, address)
);

-- Hotel bookings table
CREATE TABLE hotel_bookings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    hotel_id INT NOT NULL,
    check_in_date DATE NOT NULL,
    check_out_date DATE NOT NULL,
    room_type VARCHAR(100) NOT NULL,
    guests INT NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    status ENUM('pending', 'confirmed', 'cancelled', 'completed', 'refunded') DEFAULT 'pending',
    payment_status ENUM('pending', 'paid', 'failed', 'refunded') DEFAULT 'pending',
    payment_method ENUM('stripe', 'paypal', 'bakong') DEFAULT 'stripe',
    payment_id VARCHAR(255),
    special_requests TEXT,
    guest_names JSON, -- Array of guest names
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (hotel_id) REFERENCES hotels(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_hotel_id (hotel_id),
    INDEX idx_check_in_date (check_in_date),
    INDEX idx_status (status)
);

-- Transportation table
CREATE TABLE transportation (
    id INT PRIMARY KEY AUTO_INCREMENT,
    provider_id INT, -- Server user who provides this transportation
    vehicle_type ENUM('car', 'van', 'bus', 'motorbike', 'tuk_tuk', 'boat') NOT NULL,
    vehicle_name VARCHAR(255) NOT NULL,
    description TEXT,
    capacity INT NOT NULL,
    price_per_km DECIMAL(8,2),
    base_price DECIMAL(10,2),
    hourly_rate DECIMAL(10,2),
    driver_name VARCHAR(100),
    driver_phone VARCHAR(20),
    driver_license VARCHAR(100),
    vehicle_license VARCHAR(100),
    images JSON, -- Array of image URLs
    amenities JSON, -- ["ac", "wifi", "water", "music"]
    is_active BOOLEAN DEFAULT TRUE,
    average_rating DECIMAL(3,2) DEFAULT 0,
    total_reviews INT DEFAULT 0,
    total_bookings INT DEFAULT 0,
    current_location_lat DECIMAL(10,8),
    current_location_lng DECIMAL(11,8),
    last_location_update TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (provider_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_provider_id (provider_id),
    INDEX idx_vehicle_type (vehicle_type),
    INDEX idx_is_active (is_active),
    INDEX idx_current_location (current_location_lat, current_location_lng)
);

-- Transportation bookings table
CREATE TABLE transportation_bookings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    transportation_id INT NOT NULL,
    pickup_location VARCHAR(255) NOT NULL,
    pickup_lat DECIMAL(10,8),
    pickup_lng DECIMAL(11,8),
    dropoff_location VARCHAR(255) NOT NULL,
    dropoff_lat DECIMAL(10,8),
    dropoff_lng DECIMAL(11,8),
    pickup_date DATE NOT NULL,
    pickup_time TIME NOT NULL,
    passengers INT NOT NULL,
    distance_km DECIMAL(8,2),
    estimated_duration_minutes INT,
    total_amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    status ENUM('pending', 'confirmed', 'in_progress', 'completed', 'cancelled') DEFAULT 'pending',
    payment_status ENUM('pending', 'paid', 'failed', 'refunded') DEFAULT 'pending',
    payment_method ENUM('stripe', 'paypal', 'bakong') DEFAULT 'stripe',
    payment_id VARCHAR(255),
    special_requests TEXT,
    driver_notes TEXT,
    passenger_notes TEXT,
    actual_pickup_time TIMESTAMP,
    actual_dropoff_time TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (transportation_id) REFERENCES transportation(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_transportation_id (transportation_id),
    INDEX idx_pickup_date (pickup_date),
    INDEX idx_status (status)
);

-- Location tracking table for real-time GPS
CREATE TABLE location_tracking (
    id INT PRIMARY KEY AUTO_INCREMENT,
    booking_id INT, -- Can be transportation_booking_id or tour booking_id
    booking_type ENUM('transportation', 'tour') NOT NULL,
    latitude DECIMAL(10,8) NOT NULL,
    longitude DECIMAL(11,8) NOT NULL,
    speed DECIMAL(5,2), -- km/h
    heading DECIMAL(5,2), -- degrees
    accuracy DECIMAL(8,2), -- meters
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_booking (booking_id, booking_type),
    INDEX idx_timestamp (timestamp)
);

-- Events table for cultural activities
CREATE TABLE events (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    event_type ENUM('festival', 'market', 'ceremony', 'performance', 'workshop', 'exhibition') NOT NULL,
    location VARCHAR(255) NOT NULL,
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    start_date DATE NOT NULL,
    end_date DATE,
    start_time TIME,
    end_time TIME,
    is_recurring BOOLEAN DEFAULT FALSE,
    recurrence_pattern JSON, -- {"type": "weekly", "days": [1,3,5]} or {"type": "monthly", "date": 15}
    entry_fee DECIMAL(10,2) DEFAULT 0,
    cultural_significance TEXT,
    dress_code TEXT,
    visitor_guidelines TEXT,
    contact_info JSON, -- {"phone": "+855...", "email": "..."}
    images JSON, -- Array of image URLs
    is_active BOOLEAN DEFAULT TRUE,
    created_by INT, -- Admin user who created this event
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_event_type (event_type),
    INDEX idx_location (latitude, longitude),
    INDEX idx_start_date (start_date),
    INDEX idx_is_active (is_active),
    FULLTEXT idx_search (title, description, location)
);

-- Insert default admin user
INSERT INTO users (email, password_hash, first_name, last_name, user_type, is_verified) 
VALUES ('<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq5S/kS', 'Admin', 'User', 'admin', TRUE);
-- Default password: admin123 (hashed with bcrypt)

-- Create indexes for performance optimization
CREATE INDEX idx_users_email_type ON users(email, user_type);
CREATE INDEX idx_tours_guide_active ON tours(guide_id, is_active);
CREATE INDEX idx_bookings_user_status ON bookings(user_id, status);
CREATE INDEX idx_reviews_tour_rating ON reviews(tour_id, rating);
CREATE INDEX idx_hotels_location_active ON hotels(latitude, longitude, is_active);
CREATE INDEX idx_transportation_provider_active ON transportation(provider_id, is_active);
CREATE INDEX idx_events_date_active ON events(start_date, is_active);
