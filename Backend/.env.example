# DerLg Backend Environment Configuration
# Copy this file to .env and update with your actual values

# Server Configuration
NODE_ENV=development
PORT=5000
FRONTEND_URL=http://localhost:3000

# Database Configuration
DB_HOST=localhost
DB_PORT=3036
DB_NAME=derlgDB
DB_USER=root
DB_PASSWORD=12345

# JWT Configuration
JWT_SECRET=derlg_super_secret_jwt_key_2024_cambodia_tours
JWT_REFRESH_SECRET=derlg_refresh_secret_key_2024_secure
JWT_EXPIRE=15m
JWT_REFRESH_EXPIRE=7d

# External API Keys
STRIPE_SECRET_KEY=sk_test_placeholder_stripe_key
STRIPE_PUBLISHABLE_KEY=pk_test_placeholder_stripe_key
PAYPAL_CLIENT_ID=placeholder_paypal_client_id
PAYPAL_CLIENT_SECRET=placeholder_paypal_client_secret
GOOGLE_MAPS_API_KEY=placeholder_google_maps_api_key
OPENAI_API_KEY=placeholder_openai_api_key

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# File Upload Configuration
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf

# Security Configuration
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=logs/app.log

# External Services
WEATHER_API_KEY=placeholder_weather_api_key
CURRENCY_API_KEY=placeholder_currency_api_key

# Development/Testing
TEST_DB_NAME=derlgDB_test
ENABLE_CORS=true
ENABLE_LOGGING=true
