# DerLg Backend API Documentation

## Base URL
```
http://localhost:5000/api
```

## Authentication
All protected endpoints require a Bearer token in the Authorization header:
```
Authorization: Bearer <access_token>
```

## Response Format
All API responses follow this standard format:

### Success Response
```json
{
  "success": true,
  "data": { ... },
  "timestamp": "2025-08-20T10:30:00Z"
}
```

### Error Response
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable message",
    "details": { ... }
  },
  "timestamp": "2025-08-20T10:30:00Z"
}
```

## Authentication Endpoints

### POST /auth/register
Register a new user account.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePass123",
  "first_name": "<PERSON>",
  "last_name": "<PERSON><PERSON>",
  "phone": "+************",
  "user_type": "tourist",
  "preferred_language": "en"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": { ... },
    "tokens": {
      "accessToken": "jwt_access_token",
      "refreshToken": "jwt_refresh_token"
    }
  }
}
```

### POST /auth/login
Authenticate user and get tokens.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePass123"
}
```

### POST /auth/refresh
Refresh access token using refresh token.

**Request Body:**
```json
{
  "refreshToken": "jwt_refresh_token"
}
```

### GET /auth/profile
Get current user profile. (Protected)

### PUT /auth/profile
Update user profile. (Protected)

### POST /auth/change-password
Change user password. (Protected)

**Request Body:**
```json
{
  "currentPassword": "OldPass123",
  "newPassword": "NewPass123"
}
```

## Tour Endpoints

### GET /tours
Search and filter tours.

**Query Parameters:**
- `category` - Tour category (cultural, adventure, nature, etc.)
- `location` - Location search term
- `price_min` - Minimum price
- `price_max` - Maximum price
- `difficulty_level` - easy, moderate, challenging
- `duration_min` - Minimum duration in hours
- `duration_max` - Maximum duration in hours
- `rating_min` - Minimum rating (1-5)
- `page` - Page number (default: 1)
- `limit` - Items per page (default: 10)
- `sort_by` - Sort field (price_usd, average_rating, duration_hours, created_at)
- `sort_order` - asc or desc

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "title": "Angkor Wat Sunrise Tour",
      "description": "Experience the magical sunrise...",
      "price_usd": 45.00,
      "duration_hours": 6,
      "category": "cultural",
      "location": "Siem Reap",
      "average_rating": 4.8,
      "guide_first_name": "Sophea",
      "guide_last_name": "Chan"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 25,
    "totalPages": 3,
    "hasNext": true,
    "hasPrev": false
  }
}
```

### GET /tours/featured
Get featured tours.

### GET /tours/popular
Get popular tours.

### GET /tours/:id
Get tour details by ID.

### GET /tours/:id/availability
Check tour availability for a specific date.

**Query Parameters:**
- `date` - Date in YYYY-MM-DD format

### POST /tours
Create a new tour. (Guide only)

**Request Body:**
```json
{
  "title": "Angkor Wat Sunrise Tour",
  "description": "Experience the magical sunrise over Angkor Wat...",
  "duration_hours": 6,
  "max_participants": 8,
  "price_usd": 45.00,
  "location": "Siem Reap",
  "latitude": 13.4125,
  "longitude": 103.8670,
  "category": "cultural",
  "difficulty_level": "easy",
  "includes": "Transportation, guide, entrance fees",
  "meeting_point": "Hotel lobby",
  "cultural_significance": "UNESCO World Heritage site..."
}
```

### PUT /tours/:id
Update tour. (Guide only - own tours)

### DELETE /tours/:id
Delete tour. (Guide only - own tours)

### GET /tours/my-tours
Get tours created by authenticated guide. (Guide only)

## Booking Endpoints

### POST /bookings
Create a new booking. (Tourist only)

**Request Body:**
```json
{
  "tour_id": 1,
  "booking_date": "2025-09-15",
  "booking_time": "05:30",
  "participants": 2,
  "special_requests": "Vegetarian lunch please",
  "emergency_contact_name": "Jane Doe",
  "emergency_contact_phone": "+************"
}
```

### GET /bookings/:id
Get booking details by ID. (Owner, Guide, Admin)

### GET /bookings/my-bookings
Get current user's bookings. (Tourist)

### GET /bookings/guide-bookings
Get bookings for guide's tours. (Guide)

### GET /bookings/upcoming
Get upcoming bookings for current user.

### PUT /bookings/:id/cancel
Cancel booking. (Tourist - own bookings)

**Request Body:**
```json
{
  "reason": "Change of plans"
}
```

### PUT /bookings/:id/confirm
Confirm booking. (Guide - own tour bookings)

### PUT /bookings/:id/complete
Mark booking as completed. (Guide, Admin)

### GET /bookings/date-range
Get bookings by date range. (Guide, Admin)

**Query Parameters:**
- `start_date` - Start date (YYYY-MM-DD)
- `end_date` - End date (YYYY-MM-DD)

## Admin Endpoints

### GET /admin/dashboard
Get comprehensive dashboard statistics. (Admin only)

**Response:**
```json
{
  "success": true,
  "data": {
    "users": {
      "total": 1250,
      "tourists": 1000,
      "guides": 200,
      "servers": 45,
      "verified": 1100
    },
    "tours": {
      "total_tours": 150,
      "active_tours": 140
    },
    "bookings": {
      "total_bookings": 2500,
      "total_revenue": 125000,
      "average_booking_value": 50
    },
    "summary": {
      "total_users": 1250,
      "total_revenue": 125000,
      "average_rating": 4.6
    }
  }
}
```

### GET /admin/users
Get all users with search and filtering. (Admin only)

**Query Parameters:**
- `role` - Filter by user role
- `search` - Search term for name/email
- `page` - Page number
- `limit` - Items per page

### PUT /admin/users/:id/verify
Verify user account. (Admin only)

### PUT /admin/users/:id/deactivate
Deactivate user account. (Admin only)

### GET /admin/bookings
Get all bookings with filtering. (Admin only)

### GET /admin/reviews/pending
Get reviews pending moderation. (Admin only)

### PUT /admin/reviews/:id/moderate
Moderate review. (Admin only)

**Request Body:**
```json
{
  "is_moderated": true,
  "moderation_notes": "Approved after review"
}
```

### GET /admin/analytics
Get platform analytics. (Admin only)

### GET /admin/system/health
Get system health status. (Admin only)

### POST /admin/export
Export platform data. (Admin only)

**Query Parameters:**
- `type` - Data type to export (users, tours, bookings, reviews)
- `format` - Export format (json, csv, xlsx)

## Error Codes

| Code | Description |
|------|-------------|
| `UNAUTHORIZED` | Authentication required |
| `FORBIDDEN` | Insufficient permissions |
| `VALIDATION_ERROR` | Invalid input data |
| `NOT_FOUND` | Resource not found |
| `EMAIL_EXISTS` | Email already registered |
| `INVALID_CREDENTIALS` | Wrong email/password |
| `TOUR_NOT_AVAILABLE` | Tour not available for booking |
| `BOOKING_NOT_FOUND` | Booking not found |
| `PAYMENT_FAILED` | Payment processing failed |

## Rate Limiting
- 100 requests per 15 minutes per IP address
- Higher limits for authenticated users

## Pagination
All list endpoints support pagination with these parameters:
- `page` - Page number (starts from 1)
- `limit` - Items per page (max 100)

Pagination info is returned in the response:
```json
{
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 25,
    "totalPages": 3,
    "hasNext": true,
    "hasPrev": false
  }
}
```

## Status Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `409` - Conflict
- `500` - Internal Server Error
- `503` - Service Unavailable
