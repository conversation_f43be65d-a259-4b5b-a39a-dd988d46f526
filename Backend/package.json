{"name": "derlg-backend", "version": "1.0.0", "description": "DerLg Cambodia Tour Booking Platform - Backend API", "main": "dist/server.js", "scripts": {"start": "node dist/server.js", "dev": "nodemon --exec ts-node server.ts", "build": "tsc", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "db:migrate": "node scripts/migrate.js", "db:seed": "node scripts/seed.js"}, "keywords": ["cambodia", "tourism", "booking", "tours", "travel", "api", "nodejs", "express", "mysql"], "author": "DerLg Development Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "express-rate-limit": "^6.8.1", "dotenv": "^16.3.1", "mysql2": "^3.6.0", "bcrypt": "^5.1.0", "jsonwebtoken": "^9.0.2", "joi": "^17.9.2", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.4", "stripe": "^12.18.0", "socket.io": "^4.7.2", "winston": "^3.10.0", "express-validator": "^7.0.1", "compression": "^1.7.4", "morgan": "^1.10.0", "uuid": "^9.0.0", "moment": "^2.29.4", "axios": "^1.5.0", "sharp": "^0.32.4", "express-fileupload": "^1.4.0", "node-cron": "^3.0.2"}, "devDependencies": {"@types/express": "^4.17.17", "@types/cors": "^2.8.13", "@types/bcrypt": "^5.0.0", "@types/jsonwebtoken": "^9.0.2", "@types/multer": "^1.4.7", "@types/nodemailer": "^6.4.9", "@types/compression": "^1.7.2", "@types/morgan": "^1.9.4", "@types/uuid": "^9.0.2", "@types/node": "^20.4.8", "@types/jest": "^29.5.3", "@types/supertest": "^2.0.12", "typescript": "^5.1.6", "ts-node": "^10.9.1", "nodemon": "^3.0.1", "jest": "^29.6.2", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "@typescript-eslint/eslint-plugin": "^6.2.1", "@typescript-eslint/parser": "^6.2.1", "eslint": "^8.46.0"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/derlg/backend.git"}, "bugs": {"url": "https://github.com/derlg/backend/issues"}, "homepage": "https://derlg.com"}