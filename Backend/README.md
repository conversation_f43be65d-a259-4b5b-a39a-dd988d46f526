# DerLg Backend API

Cambodia Tour Booking Platform - Backend API Server

## 🚀 Quick Start

### Prerequisites
- Node.js 16+ 
- MySQL 8.0+
- npm or yarn

### Installation

1. **Install Dependencies**
```bash
cd Backend
npm install
```

2. **Database Setup**
```bash
# Make sure MySQL is running on localhost:3036
# Create the database and tables using the provided schema
mysql -h localhost -P 3036 -u root -p < database_schema.sql
```

3. **Environment Configuration**
```bash
# Copy environment template
cp .env.example .env

# Update .env with your actual values
# The current .env file is already configured for development
```

4. **Start Development Server**
```bash
npm run dev
```

The server will start on `http://localhost:5000`

## 📊 Database Schema

The complete database schema is provided in `database_schema.sql`. It includes:

- **users** - Multi-role user management (tourist, guide, admin, server)
- **tours** - Tour listings with comprehensive details
- **bookings** - Tour reservations and payment tracking
- **reviews** - Rating and feedback system
- **hotels** - Accommodation listings and management
- **hotel_bookings** - Hotel reservation system
- **transportation** - Vehicle listings with real-time tracking
- **transportation_bookings** - Transportation reservations
- **location_tracking** - GPS tracking for real-time location updates
- **events** - Cultural events and activities

### Database Connection
```
Host: localhost
Port: 3036
Database: derlgDB
Username: root
Password: 12345
```

## 🔐 Authentication System

### JWT Token Authentication
- **Access Token**: 15 minutes expiration
- **Refresh Token**: 7 days expiration
- **Multi-role Support**: tourist, guide, admin, server

### API Endpoints

#### Authentication
```
POST /api/auth/register     - Register new user
POST /api/auth/login        - User login
POST /api/auth/refresh      - Refresh access token
GET  /api/auth/profile      - Get user profile
PUT  /api/auth/profile      - Update user profile
POST /api/auth/change-password - Change password
POST /api/auth/logout       - Logout user
```

### User Roles
- **Tourist**: Book tours, hotels, transportation
- **Guide**: Manage tours, view bookings
- **Server**: Manage hotels and transportation
- **Admin**: Full platform management

## 🛠️ Development

### Available Scripts
```bash
npm run dev         # Start development server with hot reload
npm run build       # Build TypeScript to JavaScript
npm start           # Start production server
npm test            # Run tests
npm run lint        # Run ESLint
```

### Project Structure
```
Backend/
├── src/
│   ├── config/         # Database and app configuration
│   ├── controllers/    # Request handlers
│   ├── middleware/     # Authentication, validation, etc.
│   ├── models/         # Database models
│   ├── routes/         # API route definitions
│   ├── types/          # TypeScript type definitions
│   └── utils/          # Utility functions
├── database_schema.sql # Complete database schema
├── server.ts          # Main server file
├── package.json       # Dependencies and scripts
└── tsconfig.json      # TypeScript configuration
```

## 🔧 API Documentation

### Response Format
All API responses follow this format:
```json
{
  "success": true,
  "data": { ... },
  "timestamp": "2025-08-20T10:30:00Z"
}
```

### Error Response Format
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable message",
    "details": { ... }
  },
  "timestamp": "2025-08-20T10:30:00Z"
}
```

### Authentication Headers
```
Authorization: Bearer <access_token>
```

## 🚦 Health Check

Check server and database status:
```
GET /health
```

Response:
```json
{
  "status": "OK",
  "message": "DerLg Backend Server is running",
  "database": {
    "status": "healthy",
    "message": "Database connection is working"
  },
  "timestamp": "2025-08-20T10:30:00Z"
}
```

## 🔒 Security Features

- **JWT Authentication** with refresh tokens
- **Password Hashing** using bcrypt (12 rounds)
- **Input Validation** with Joi schemas
- **Rate Limiting** (100 requests per 15 minutes)
- **CORS Protection** 
- **Helmet Security Headers**
- **SQL Injection Protection** with parameterized queries

## 🌍 Environment Variables

Key environment variables in `.env`:

```env
# Server
NODE_ENV=development
PORT=5000

# Database
DB_HOST=localhost
DB_PORT=3036
DB_NAME=derlgDB
DB_USER=root
DB_PASSWORD=12345

# JWT
JWT_SECRET=derlg_super_secret_jwt_key_2024_cambodia_tours
JWT_REFRESH_SECRET=derlg_refresh_secret_key_2024_secure

# External APIs (update with real keys)
STRIPE_SECRET_KEY=sk_test_placeholder
GOOGLE_MAPS_API_KEY=placeholder
OPENAI_API_KEY=placeholder
```

## 📝 Next Steps

1. **Install Dependencies**: `npm install`
2. **Setup Database**: Run the SQL schema file
3. **Start Development**: `npm run dev`
4. **Test Authentication**: Use the auth endpoints
5. **Add More Features**: Tours, bookings, hotels, etc.

## 🐛 Troubleshooting

### Database Connection Issues
- Ensure MySQL is running on port 3036
- Check database credentials in `.env`
- Verify database `derlgDB` exists

### TypeScript Errors
- Run `npm install` to install all dependencies
- Check `tsconfig.json` configuration
- Ensure all imports are correct

### Port Already in Use
- Change PORT in `.env` file
- Kill existing process: `lsof -ti:5000 | xargs kill`

## 📞 Support

For issues and questions:
- Check the documentation in `/document` folder
- Review the implementation guidelines
- Test with the provided database schema

---

**Status**: ✅ Authentication System Complete  
**Next**: Core API Endpoints (Tours, Bookings, Hotels)  
**Version**: 1.0.0
