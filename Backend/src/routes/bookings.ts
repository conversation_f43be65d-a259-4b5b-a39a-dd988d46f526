import { Router } from 'express';
import { BookingController } from '../controllers/bookingController';
import { authenticateToken, authorizeRoles } from '../middleware/auth';
import { validate, validateQuery, createBookingSchema, paginationSchema } from '../middleware/validation';
import Joi from 'joi';

const router = Router();

// Booking cancellation schema
const cancelBookingSchema = Joi.object({
  reason: Joi.string().max(500).optional().messages({
    'string.max': 'Cancellation reason cannot exceed 500 characters'
  })
});

// Date range query schema
const dateRangeSchema = Joi.object({
  start_date: Joi.date().required().messages({
    'any.required': 'Start date is required'
  }),
  end_date: Joi.date().min(Joi.ref('start_date')).required().messages({
    'date.min': 'End date must be after start date',
    'any.required': 'End date is required'
  })
});

/**
 * @route   GET /api/bookings/my-bookings
 * @desc    Get current user's bookings
 * @access  Private (Tourist)
 * @query   { page?, limit? }
 */
router.get('/my-bookings', authenticateToken, authorizeRoles('tourist'), validateQuery(paginationSchema), BookingController.getUserBookings);

/**
 * @route   GET /api/bookings/guide-bookings
 * @desc    Get bookings for guide's tours
 * @access  Private (Guide)
 * @query   { page?, limit? }
 */
router.get('/guide-bookings', authenticateToken, authorizeRoles('guide'), validateQuery(paginationSchema), BookingController.getGuideBookings);

/**
 * @route   GET /api/bookings/upcoming
 * @desc    Get upcoming bookings for current user
 * @access  Private
 * @query   { limit? }
 */
router.get('/upcoming', authenticateToken, BookingController.getUpcomingBookings);

/**
 * @route   GET /api/bookings/stats
 * @desc    Get booking statistics
 * @access  Private (Admin only)
 */
router.get('/stats', authenticateToken, authorizeRoles('admin'), BookingController.getBookingStats);

/**
 * @route   GET /api/bookings/date-range
 * @desc    Get bookings by date range
 * @access  Private (Guide, Admin)
 * @query   { start_date, end_date }
 */
router.get('/date-range', authenticateToken, authorizeRoles('guide', 'admin'), validateQuery(dateRangeSchema), BookingController.getBookingsByDateRange);

/**
 * @route   GET /api/bookings/:id
 * @desc    Get booking by ID
 * @access  Private (Owner, Guide, Admin)
 */
router.get('/:id', authenticateToken, BookingController.getBooking);

/**
 * @route   POST /api/bookings
 * @desc    Create a new booking
 * @access  Private (Tourist)
 * @body    { tour_id, booking_date, booking_time?, participants, special_requests?, emergency_contact_name?, emergency_contact_phone? }
 */
router.post('/', authenticateToken, authorizeRoles('tourist'), validate(createBookingSchema), BookingController.createBooking);

/**
 * @route   PUT /api/bookings/:id/cancel
 * @desc    Cancel booking
 * @access  Private (Tourist - own bookings)
 * @body    { reason? }
 */
router.put('/:id/cancel', authenticateToken, authorizeRoles('tourist'), validate(cancelBookingSchema), BookingController.cancelBooking);

/**
 * @route   PUT /api/bookings/:id/confirm
 * @desc    Confirm booking
 * @access  Private (Guide - own tour bookings)
 */
router.put('/:id/confirm', authenticateToken, authorizeRoles('guide'), BookingController.confirmBooking);

/**
 * @route   PUT /api/bookings/:id/complete
 * @desc    Mark booking as completed
 * @access  Private (Guide, Admin)
 */
router.put('/:id/complete', authenticateToken, authorizeRoles('guide', 'admin'), BookingController.completeBooking);

export default router;
