import { Router } from 'express';
import { TourController } from '../controllers/tourController';
import { authenticateToken, authorizeR<PERSON>s, optionalAuth } from '../middleware/auth';
import { validate, validateQuery, createTourSchema, paginationSchema } from '../middleware/validation';
import Joi from 'joi';

const router = Router();

// Tour search filters validation schema
const tourSearchSchema = Joi.object({
  category: Joi.string().valid('cultural', 'adventure', 'nature', 'food', 'historical', 'religious', 'shopping').optional(),
  location: Joi.string().min(2).max(100).optional(),
  price_min: Joi.number().min(0).optional(),
  price_max: Joi.number().min(0).optional(),
  difficulty_level: Joi.string().valid('easy', 'moderate', 'challenging').optional(),
  duration_min: Joi.number().integer().min(1).optional(),
  duration_max: Joi.number().integer().min(1).optional(),
  rating_min: Joi.number().min(1).max(5).optional(),
  date_from: Joi.date().optional(),
  date_to: Joi.date().min(Joi.ref('date_from')).optional(),
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(50).default(10),
  sort_by: Joi.string().valid('price_usd', 'average_rating', 'duration_hours', 'created_at', 'total_reviews').default('created_at'),
  sort_order: Joi.string().valid('asc', 'desc').default('desc')
});

const availabilitySchema = Joi.object({
  date: Joi.date().min('now').required().messages({
    'date.min': 'Date cannot be in the past',
    'any.required': 'Date is required'
  })
});

/**
 * @route   GET /api/tours
 * @desc    Search and filter tours
 * @access  Public
 * @query   { category?, location?, price_min?, price_max?, difficulty_level?, duration_min?, duration_max?, rating_min?, date_from?, date_to?, page?, limit?, sort_by?, sort_order? }
 */
router.get('/', validateQuery(tourSearchSchema), TourController.searchTours);

/**
 * @route   GET /api/tours/featured
 * @desc    Get featured tours
 * @access  Public
 * @query   { limit? }
 */
router.get('/featured', TourController.getFeaturedTours);

/**
 * @route   GET /api/tours/popular
 * @desc    Get popular tours
 * @access  Public
 * @query   { limit? }
 */
router.get('/popular', TourController.getPopularTours);

/**
 * @route   GET /api/tours/my-tours
 * @desc    Get tours created by the authenticated guide
 * @access  Private (Guide only)
 * @query   { page?, limit? }
 */
router.get('/my-tours', authenticateToken, authorizeRoles('guide'), validateQuery(paginationSchema), TourController.getGuideTours);

/**
 * @route   GET /api/tours/stats
 * @desc    Get tour statistics
 * @access  Private (Admin only)
 */
router.get('/stats', authenticateToken, authorizeRoles('admin'), TourController.getTourStats);

/**
 * @route   GET /api/tours/:id
 * @desc    Get tour by ID
 * @access  Public
 */
router.get('/:id', TourController.getTour);

/**
 * @route   GET /api/tours/:id/availability
 * @desc    Check tour availability for a specific date
 * @access  Public
 * @query   { date }
 */
router.get('/:id/availability', validateQuery(availabilitySchema), TourController.checkAvailability);

/**
 * @route   POST /api/tours
 * @desc    Create a new tour
 * @access  Private (Guide only)
 * @body    { title, description, duration_hours, max_participants, price_usd, location, latitude?, longitude?, category, difficulty_level?, includes?, excludes?, requirements?, cancellation_policy?, meeting_point?, meeting_point_lat?, meeting_point_lng?, cultural_significance?, dress_code? }
 */
router.post('/', authenticateToken, authorizeRoles('guide'), validate(createTourSchema), TourController.createTour);

/**
 * @route   PUT /api/tours/:id
 * @desc    Update tour
 * @access  Private (Guide only - own tours)
 * @body    { title?, description?, duration_hours?, max_participants?, price_usd?, location?, latitude?, longitude?, category?, difficulty_level?, includes?, excludes?, requirements?, cancellation_policy?, meeting_point?, meeting_point_lat?, meeting_point_lng?, cultural_significance?, dress_code? }
 */
router.put('/:id', authenticateToken, authorizeRoles('guide'), validate(createTourSchema.fork(Object.keys(createTourSchema.describe().keys), (schema) => schema.optional())), TourController.updateTour);

/**
 * @route   DELETE /api/tours/:id
 * @desc    Delete tour (soft delete)
 * @access  Private (Guide only - own tours)
 */
router.delete('/:id', authenticateToken, authorizeRoles('guide'), TourController.deleteTour);

export default router;
