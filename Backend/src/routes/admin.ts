import { Router } from 'express';
import { AdminController } from '../controllers/adminController';
import { authenticateToken, authorizeRoles } from '../middleware/auth';
import { validate, validateQuery, paginationSchema } from '../middleware/validation';
import Joi from 'joi';

const router = Router();

// All admin routes require admin authentication
router.use(authenticateToken);
router.use(authorizeRoles('admin'));

// User search schema
const userSearchSchema = Joi.object({
  role: Joi.string().valid('tourist', 'guide', 'server', 'admin').optional(),
  search: Joi.string().min(2).max(100).optional(),
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(20)
});

// Review moderation schema
const moderateReviewSchema = Joi.object({
  is_moderated: Joi.boolean().required(),
  moderation_notes: Joi.string().max(500).optional()
});

// Analytics query schema
const analyticsSchema = Joi.object({
  period: Joi.string().valid('7d', '30d', '90d', '1y').default('30d')
});

// Export data schema
const exportSchema = Joi.object({
  type: Joi.string().valid('users', 'tours', 'bookings', 'reviews', 'hotels', 'events').required(),
  format: Joi.string().valid('json', 'csv', 'xlsx').default('json')
});

// Date range schema
const dateRangeSchema = Joi.object({
  start_date: Joi.date().optional(),
  end_date: Joi.date().min(Joi.ref('start_date')).optional(),
  status: Joi.string().valid('pending', 'confirmed', 'cancelled', 'completed', 'refunded').optional(),
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(20)
});

/**
 * @route   GET /api/admin/dashboard
 * @desc    Get comprehensive dashboard statistics
 * @access  Private (Admin only)
 */
router.get('/dashboard', AdminController.getDashboardStats);

/**
 * @route   GET /api/admin/users
 * @desc    Get all users with search and filtering
 * @access  Private (Admin only)
 * @query   { role?, search?, page?, limit? }
 */
router.get('/users', validateQuery(userSearchSchema), AdminController.getUsers);

/**
 * @route   PUT /api/admin/users/:id/verify
 * @desc    Verify user account
 * @access  Private (Admin only)
 */
router.put('/users/:id/verify', AdminController.verifyUser);

/**
 * @route   PUT /api/admin/users/:id/deactivate
 * @desc    Deactivate user account
 * @access  Private (Admin only)
 */
router.put('/users/:id/deactivate', AdminController.deactivateUser);

/**
 * @route   GET /api/admin/bookings
 * @desc    Get all bookings with filtering
 * @access  Private (Admin only)
 * @query   { status?, start_date?, end_date?, page?, limit? }
 */
router.get('/bookings', validateQuery(dateRangeSchema), AdminController.getAllBookings);

/**
 * @route   GET /api/admin/reviews/pending
 * @desc    Get reviews pending moderation
 * @access  Private (Admin only)
 * @query   { page?, limit? }
 */
router.get('/reviews/pending', validateQuery(paginationSchema), AdminController.getPendingReviews);

/**
 * @route   PUT /api/admin/reviews/:id/moderate
 * @desc    Moderate review
 * @access  Private (Admin only)
 * @body    { is_moderated, moderation_notes? }
 */
router.put('/reviews/:id/moderate', validate(moderateReviewSchema), AdminController.moderateReview);

/**
 * @route   GET /api/admin/analytics
 * @desc    Get platform analytics
 * @access  Private (Admin only)
 * @query   { period? }
 */
router.get('/analytics', validateQuery(analyticsSchema), AdminController.getAnalytics);

/**
 * @route   GET /api/admin/system/health
 * @desc    Get system health status
 * @access  Private (Admin only)
 */
router.get('/system/health', AdminController.getSystemHealth);

/**
 * @route   POST /api/admin/export
 * @desc    Export platform data
 * @access  Private (Admin only)
 * @query   { type, format? }
 */
router.post('/export', validateQuery(exportSchema), AdminController.exportData);

export default router;
