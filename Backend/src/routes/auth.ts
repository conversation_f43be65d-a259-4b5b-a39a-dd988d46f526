import { Router } from 'express';
import { AuthController } from '../controllers/authController';
import { validate, registerSchema, loginSchema } from '../middleware/validation';
import { authenticateToken } from '../middleware/auth';
import Joi from 'joi';

const router = Router();

// Validation schemas for auth routes
const refreshTokenSchema = Joi.object({
  refreshToken: Joi.string().required().messages({
    'any.required': 'Refresh token is required'
  })
});

const changePasswordSchema = Joi.object({
  currentPassword: Joi.string().required().messages({
    'any.required': 'Current password is required'
  }),
  newPassword: Joi.string().min(8).pattern(new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)')).required().messages({
    'string.min': 'New password must be at least 8 characters long',
    'string.pattern.base': 'New password must contain at least one uppercase letter, one lowercase letter, and one number',
    'any.required': 'New password is required'
  })
});

const updateProfileSchema = Joi.object({
  first_name: Joi.string().min(2).max(50).optional(),
  last_name: Joi.string().min(2).max(50).optional(),
  phone: Joi.string().pattern(new RegExp('^\\+?[1-9]\\d{1,14}$')).optional(),
  preferred_language: Joi.string().valid('en', 'km', 'zh').optional(),
  bio: Joi.string().max(1000).optional(),
  emergency_contact_name: Joi.string().min(2).max(100).optional(),
  emergency_contact_phone: Joi.string().pattern(new RegExp('^\\+?[1-9]\\d{1,14}$')).optional(),
  specializations: Joi.array().items(Joi.string().valid('cultural', 'adventure', 'nature', 'food', 'historical', 'religious', 'shopping')).optional(),
  languages_spoken: Joi.array().items(Joi.string()).optional()
});

/**
 * @route   POST /api/auth/register
 * @desc    Register a new user
 * @access  Public
 * @body    { email, password, first_name, last_name, phone?, user_type?, preferred_language? }
 */
router.post('/register', validate(registerSchema), AuthController.register);

/**
 * @route   POST /api/auth/login
 * @desc    Login user
 * @access  Public
 * @body    { email, password }
 */
router.post('/login', validate(loginSchema), AuthController.login);

/**
 * @route   POST /api/auth/refresh
 * @desc    Refresh access token
 * @access  Public
 * @body    { refreshToken }
 */
router.post('/refresh', validate(refreshTokenSchema), AuthController.refreshToken);

/**
 * @route   GET /api/auth/profile
 * @desc    Get current user profile
 * @access  Private
 */
router.get('/profile', authenticateToken, AuthController.getProfile);

/**
 * @route   PUT /api/auth/profile
 * @desc    Update user profile
 * @access  Private
 * @body    { first_name?, last_name?, phone?, preferred_language?, bio?, emergency_contact_name?, emergency_contact_phone?, specializations?, languages_spoken? }
 */
router.put('/profile', authenticateToken, validate(updateProfileSchema), AuthController.updateProfile);

/**
 * @route   POST /api/auth/change-password
 * @desc    Change user password
 * @access  Private
 * @body    { currentPassword, newPassword }
 */
router.post('/change-password', authenticateToken, validate(changePasswordSchema), AuthController.changePassword);

/**
 * @route   POST /api/auth/logout
 * @desc    Logout user (client-side token removal)
 * @access  Private
 */
router.post('/logout', authenticateToken, AuthController.logout);

export default router;
