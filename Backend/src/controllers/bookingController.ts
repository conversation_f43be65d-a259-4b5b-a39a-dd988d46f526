import { Request, Response } from 'express';
import { BookingModel } from '../models/Booking';
import { TourModel } from '../models/Tour';
import { CreateBookingRequest } from '../types';

export class BookingController {
  // Create new booking
  static async createBooking(req: Request, res: Response): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Authentication required',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      const bookingData: CreateBookingRequest = req.body;

      // Check tour availability
      const available = await TourModel.checkAvailability(bookingData.tour_id, bookingData.booking_date);
      
      if (!available) {
        res.status(400).json({
          success: false,
          error: {
            code: 'TOUR_NOT_AVAILABLE',
            message: 'Tour is not available for the selected date',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      const booking = await BookingModel.create(req.user.id, bookingData);

      res.status(201).json({
        success: true,
        data: booking,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Create booking error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'BOOKING_CREATION_FAILED',
          message: 'Failed to create booking',
          timestamp: new Date().toISOString()
        }
      });
    }
  }

  // Get booking by ID
  static async getBooking(req: Request, res: Response): Promise<void> {
    try {
      const bookingId = parseInt(req.params.id);
      
      if (isNaN(bookingId)) {
        res.status(400).json({
          success: false,
          error: {
            code: 'INVALID_BOOKING_ID',
            message: 'Invalid booking ID',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      const booking = await BookingModel.findById(bookingId);
      
      if (!booking) {
        res.status(404).json({
          success: false,
          error: {
            code: 'BOOKING_NOT_FOUND',
            message: 'Booking not found',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      // Check if user has permission to view this booking
      if (req.user && (req.user.user_type === 'admin' || 
          booking.user_id === req.user.id || 
          booking.tour_guide_id === req.user.id)) {
        res.json({
          success: true,
          data: booking,
          timestamp: new Date().toISOString()
        });
      } else {
        res.status(403).json({
          success: false,
          error: {
            code: 'FORBIDDEN',
            message: 'You do not have permission to view this booking',
            timestamp: new Date().toISOString()
          }
        });
      }
    } catch (error) {
      console.error('Get booking error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'BOOKING_FETCH_FAILED',
          message: 'Failed to fetch booking',
          timestamp: new Date().toISOString()
        }
      });
    }
  }

  // Get user's bookings
  static async getUserBookings(req: Request, res: Response): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Authentication required',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      const page = req.query.page ? parseInt(req.query.page as string) : 1;
      const limit = req.query.limit ? parseInt(req.query.limit as string) : 10;

      const result = await BookingModel.getByUser(req.user.id, page, limit);

      res.json({
        success: true,
        data: result.data,
        pagination: result.pagination,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Get user bookings error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'USER_BOOKINGS_FETCH_FAILED',
          message: 'Failed to fetch user bookings',
          timestamp: new Date().toISOString()
        }
      });
    }
  }

  // Get guide's bookings
  static async getGuideBookings(req: Request, res: Response): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Authentication required',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      const page = req.query.page ? parseInt(req.query.page as string) : 1;
      const limit = req.query.limit ? parseInt(req.query.limit as string) : 10;

      const result = await BookingModel.getByGuide(req.user.id, page, limit);

      res.json({
        success: true,
        data: result.data,
        pagination: result.pagination,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Get guide bookings error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'GUIDE_BOOKINGS_FETCH_FAILED',
          message: 'Failed to fetch guide bookings',
          timestamp: new Date().toISOString()
        }
      });
    }
  }

  // Cancel booking
  static async cancelBooking(req: Request, res: Response): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Authentication required',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      const bookingId = parseInt(req.params.id);
      const { reason } = req.body;

      if (isNaN(bookingId)) {
        res.status(400).json({
          success: false,
          error: {
            code: 'INVALID_BOOKING_ID',
            message: 'Invalid booking ID',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      const cancelledBooking = await BookingModel.cancel(bookingId, req.user.id, reason);
      
      if (!cancelledBooking) {
        res.status(404).json({
          success: false,
          error: {
            code: 'BOOKING_NOT_FOUND',
            message: 'Booking not found or cannot be cancelled',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      res.json({
        success: true,
        data: cancelledBooking,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Cancel booking error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'BOOKING_CANCELLATION_FAILED',
          message: 'Failed to cancel booking',
          timestamp: new Date().toISOString()
        }
      });
    }
  }

  // Confirm booking (Guide only)
  static async confirmBooking(req: Request, res: Response): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Authentication required',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      const bookingId = parseInt(req.params.id);

      if (isNaN(bookingId)) {
        res.status(400).json({
          success: false,
          error: {
            code: 'INVALID_BOOKING_ID',
            message: 'Invalid booking ID',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      const confirmedBooking = await BookingModel.confirm(bookingId, req.user.id);
      
      if (!confirmedBooking) {
        res.status(404).json({
          success: false,
          error: {
            code: 'BOOKING_NOT_FOUND',
            message: 'Booking not found or you do not have permission to confirm it',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      res.json({
        success: true,
        data: confirmedBooking,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Confirm booking error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'BOOKING_CONFIRMATION_FAILED',
          message: 'Failed to confirm booking',
          timestamp: new Date().toISOString()
        }
      });
    }
  }

  // Complete booking
  static async completeBooking(req: Request, res: Response): Promise<void> {
    try {
      const bookingId = parseInt(req.params.id);

      if (isNaN(bookingId)) {
        res.status(400).json({
          success: false,
          error: {
            code: 'INVALID_BOOKING_ID',
            message: 'Invalid booking ID',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      const completedBooking = await BookingModel.complete(bookingId);
      
      if (!completedBooking) {
        res.status(404).json({
          success: false,
          error: {
            code: 'BOOKING_NOT_FOUND',
            message: 'Booking not found or cannot be completed',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      res.json({
        success: true,
        data: completedBooking,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Complete booking error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'BOOKING_COMPLETION_FAILED',
          message: 'Failed to complete booking',
          timestamp: new Date().toISOString()
        }
      });
    }
  }

  // Get upcoming bookings
  static async getUpcomingBookings(req: Request, res: Response): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Authentication required',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      const limit = req.query.limit ? parseInt(req.query.limit as string) : 10;
      
      let userId: number | undefined;
      let guideId: number | undefined;

      if (req.user.user_type === 'tourist') {
        userId = req.user.id;
      } else if (req.user.user_type === 'guide') {
        guideId = req.user.id;
      }

      const bookings = await BookingModel.getUpcoming(userId, guideId, limit);

      res.json({
        success: true,
        data: bookings,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Get upcoming bookings error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'UPCOMING_BOOKINGS_FETCH_FAILED',
          message: 'Failed to fetch upcoming bookings',
          timestamp: new Date().toISOString()
        }
      });
    }
  }

  // Get booking statistics (Admin only)
  static async getBookingStats(req: Request, res: Response): Promise<void> {
    try {
      const stats = await BookingModel.getStats();

      res.json({
        success: true,
        data: stats,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Get booking stats error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'BOOKING_STATS_FETCH_FAILED',
          message: 'Failed to fetch booking statistics',
          timestamp: new Date().toISOString()
        }
      });
    }
  }

  // Get bookings by date range
  static async getBookingsByDateRange(req: Request, res: Response): Promise<void> {
    try {
      const { start_date, end_date } = req.query;

      if (!start_date || !end_date) {
        res.status(400).json({
          success: false,
          error: {
            code: 'MISSING_DATE_RANGE',
            message: 'Start date and end date are required',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      let guideId: number | undefined;
      if (req.user && req.user.user_type === 'guide') {
        guideId = req.user.id;
      }

      const bookings = await BookingModel.getByDateRange(
        start_date as string, 
        end_date as string, 
        guideId
      );

      res.json({
        success: true,
        data: bookings,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Get bookings by date range error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'DATE_RANGE_BOOKINGS_FETCH_FAILED',
          message: 'Failed to fetch bookings by date range',
          timestamp: new Date().toISOString()
        }
      });
    }
  }
}
