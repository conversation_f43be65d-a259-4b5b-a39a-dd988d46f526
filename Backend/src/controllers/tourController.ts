import { Request, Response } from 'express';
import { TourModel } from '../models/Tour';
import { CreateTourRequest, TourSearchFilters } from '../types';

export class TourController {
  // Create new tour (Guide only)
  static async createTour(req: Request, res: Response): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Authentication required',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      const tourData: CreateTourRequest = req.body;
      const tour = await TourModel.create(req.user.id, tourData);

      res.status(201).json({
        success: true,
        data: tour,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Create tour error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'TOUR_CREATION_FAILED',
          message: 'Failed to create tour',
          timestamp: new Date().toISOString()
        }
      });
    }
  }

  // Get tour by ID
  static async getTour(req: Request, res: Response): Promise<void> {
    try {
      const tourId = parseInt(req.params.id);
      
      if (isNaN(tourId)) {
        res.status(400).json({
          success: false,
          error: {
            code: 'INVALID_TOUR_ID',
            message: 'Invalid tour ID',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      const tour = await TourModel.findById(tourId);
      
      if (!tour) {
        res.status(404).json({
          success: false,
          error: {
            code: 'TOUR_NOT_FOUND',
            message: 'Tour not found',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      res.json({
        success: true,
        data: tour,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Get tour error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'TOUR_FETCH_FAILED',
          message: 'Failed to fetch tour',
          timestamp: new Date().toISOString()
        }
      });
    }
  }

  // Search tours with filters
  static async searchTours(req: Request, res: Response): Promise<void> {
    try {
      const filters: TourSearchFilters = {
        category: req.query.category as string,
        location: req.query.location as string,
        price_min: req.query.price_min ? parseFloat(req.query.price_min as string) : undefined,
        price_max: req.query.price_max ? parseFloat(req.query.price_max as string) : undefined,
        difficulty_level: req.query.difficulty_level as string,
        duration_min: req.query.duration_min ? parseInt(req.query.duration_min as string) : undefined,
        duration_max: req.query.duration_max ? parseInt(req.query.duration_max as string) : undefined,
        rating_min: req.query.rating_min ? parseFloat(req.query.rating_min as string) : undefined,
        date_from: req.query.date_from as string,
        date_to: req.query.date_to as string,
        page: req.query.page ? parseInt(req.query.page as string) : 1,
        limit: req.query.limit ? parseInt(req.query.limit as string) : 10,
        sort_by: req.query.sort_by as 'price' | 'rating' | 'duration' | 'created_at',
        sort_order: req.query.sort_order as 'asc' | 'desc'
      };

      const result = await TourModel.search(filters);

      res.json({
        success: true,
        data: result.data,
        pagination: result.pagination,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Search tours error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'TOUR_SEARCH_FAILED',
          message: 'Failed to search tours',
          timestamp: new Date().toISOString()
        }
      });
    }
  }

  // Get tours by guide (Guide only)
  static async getGuideTours(req: Request, res: Response): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Authentication required',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      const page = req.query.page ? parseInt(req.query.page as string) : 1;
      const limit = req.query.limit ? parseInt(req.query.limit as string) : 10;

      const result = await TourModel.getByGuide(req.user.id, page, limit);

      res.json({
        success: true,
        data: result.data,
        pagination: result.pagination,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Get guide tours error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'GUIDE_TOURS_FETCH_FAILED',
          message: 'Failed to fetch guide tours',
          timestamp: new Date().toISOString()
        }
      });
    }
  }

  // Update tour (Guide only)
  static async updateTour(req: Request, res: Response): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Authentication required',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      const tourId = parseInt(req.params.id);
      
      if (isNaN(tourId)) {
        res.status(400).json({
          success: false,
          error: {
            code: 'INVALID_TOUR_ID',
            message: 'Invalid tour ID',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      const updatedTour = await TourModel.update(tourId, req.user.id, req.body);
      
      if (!updatedTour) {
        res.status(404).json({
          success: false,
          error: {
            code: 'TOUR_NOT_FOUND',
            message: 'Tour not found or you do not have permission to update it',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      res.json({
        success: true,
        data: updatedTour,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Update tour error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'TOUR_UPDATE_FAILED',
          message: 'Failed to update tour',
          timestamp: new Date().toISOString()
        }
      });
    }
  }

  // Delete tour (Guide only)
  static async deleteTour(req: Request, res: Response): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Authentication required',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      const tourId = parseInt(req.params.id);
      
      if (isNaN(tourId)) {
        res.status(400).json({
          success: false,
          error: {
            code: 'INVALID_TOUR_ID',
            message: 'Invalid tour ID',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      const deleted = await TourModel.delete(tourId, req.user.id);
      
      if (!deleted) {
        res.status(404).json({
          success: false,
          error: {
            code: 'TOUR_NOT_FOUND',
            message: 'Tour not found or you do not have permission to delete it',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      res.json({
        success: true,
        data: {
          message: 'Tour deleted successfully'
        },
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Delete tour error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'TOUR_DELETE_FAILED',
          message: 'Failed to delete tour',
          timestamp: new Date().toISOString()
        }
      });
    }
  }

  // Get featured tours
  static async getFeaturedTours(req: Request, res: Response): Promise<void> {
    try {
      const limit = req.query.limit ? parseInt(req.query.limit as string) : 6;
      const tours = await TourModel.getFeatured(limit);

      res.json({
        success: true,
        data: tours,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Get featured tours error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'FEATURED_TOURS_FETCH_FAILED',
          message: 'Failed to fetch featured tours',
          timestamp: new Date().toISOString()
        }
      });
    }
  }

  // Get popular tours
  static async getPopularTours(req: Request, res: Response): Promise<void> {
    try {
      const limit = req.query.limit ? parseInt(req.query.limit as string) : 6;
      const tours = await TourModel.getPopular(limit);

      res.json({
        success: true,
        data: tours,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Get popular tours error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'POPULAR_TOURS_FETCH_FAILED',
          message: 'Failed to fetch popular tours',
          timestamp: new Date().toISOString()
        }
      });
    }
  }

  // Check tour availability
  static async checkAvailability(req: Request, res: Response): Promise<void> {
    try {
      const tourId = parseInt(req.params.id);
      const { date } = req.query;

      if (isNaN(tourId) || !date) {
        res.status(400).json({
          success: false,
          error: {
            code: 'INVALID_PARAMETERS',
            message: 'Tour ID and date are required',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      const available = await TourModel.checkAvailability(tourId, date as string);

      res.json({
        success: true,
        data: {
          available,
          tour_id: tourId,
          date: date
        },
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Check availability error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'AVAILABILITY_CHECK_FAILED',
          message: 'Failed to check tour availability',
          timestamp: new Date().toISOString()
        }
      });
    }
  }

  // Get tour statistics (Admin only)
  static async getTourStats(req: Request, res: Response): Promise<void> {
    try {
      const stats = await TourModel.getStats();

      res.json({
        success: true,
        data: stats,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Get tour stats error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'TOUR_STATS_FETCH_FAILED',
          message: 'Failed to fetch tour statistics',
          timestamp: new Date().toISOString()
        }
      });
    }
  }
}
