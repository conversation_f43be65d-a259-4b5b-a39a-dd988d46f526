import { Request, Response } from 'express';
import { UserModel } from '../models/User';
import { TourModel } from '../models/Tour';
import { BookingModel } from '../models/Booking';
import { ReviewModel } from '../models/Review';
import { HotelModel } from '../models/Hotel';
import { EventModel } from '../models/Event';

export class AdminController {
  // Get comprehensive dashboard statistics
  static async getDashboardStats(req: Request, res: Response): Promise<void> {
    try {
      const [
        userStats,
        tourStats,
        bookingStats,
        reviewStats,
        hotelStats,
        eventStats
      ] = await Promise.all([
        UserModel.getUserStats(),
        TourModel.getStats(),
        BookingModel.getStats(),
        ReviewModel.getStats(),
        HotelModel.getStats(),
        EventModel.getStats()
      ]);

      const dashboardData = {
        users: userStats,
        tours: tourStats,
        bookings: bookingStats,
        reviews: reviewStats,
        hotels: hotelStats,
        events: eventStats,
        summary: {
          total_users: userStats.total,
          total_tours: tourStats.total_tours || 0,
          total_bookings: bookingStats.total_bookings || 0,
          total_revenue: bookingStats.total_revenue || 0,
          average_rating: reviewStats.average_rating || 0,
          total_hotels: hotelStats.total_hotels || 0,
          total_events: eventStats.total_events || 0
        }
      };

      res.json({
        success: true,
        data: dashboardData,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Get dashboard stats error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'DASHBOARD_STATS_FAILED',
          message: 'Failed to fetch dashboard statistics',
          timestamp: new Date().toISOString()
        }
      });
    }
  }

  // Get all users with search and filtering
  static async getUsers(req: Request, res: Response): Promise<void> {
    try {
      const {
        role,
        search,
        page = 1,
        limit = 20
      } = req.query;

      const result = await UserModel.searchUsers(
        role as string,
        search as string,
        parseInt(page as string),
        parseInt(limit as string)
      );

      res.json({
        success: true,
        data: result.users,
        pagination: {
          page: parseInt(page as string),
          limit: parseInt(limit as string),
          total: result.total,
          totalPages: Math.ceil(result.total / parseInt(limit as string)),
          hasNext: parseInt(page as string) * parseInt(limit as string) < result.total,
          hasPrev: parseInt(page as string) > 1
        },
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Get users error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'USERS_FETCH_FAILED',
          message: 'Failed to fetch users',
          timestamp: new Date().toISOString()
        }
      });
    }
  }

  // Verify user account
  static async verifyUser(req: Request, res: Response): Promise<void> {
    try {
      const userId = parseInt(req.params.id);

      if (isNaN(userId)) {
        res.status(400).json({
          success: false,
          error: {
            code: 'INVALID_USER_ID',
            message: 'Invalid user ID',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      const success = await UserModel.verifyAccount(userId);

      if (!success) {
        res.status(404).json({
          success: false,
          error: {
            code: 'USER_NOT_FOUND',
            message: 'User not found',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      res.json({
        success: true,
        data: {
          message: 'User verified successfully',
          user_id: userId
        },
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Verify user error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'USER_VERIFICATION_FAILED',
          message: 'Failed to verify user',
          timestamp: new Date().toISOString()
        }
      });
    }
  }

  // Deactivate user account
  static async deactivateUser(req: Request, res: Response): Promise<void> {
    try {
      const userId = parseInt(req.params.id);

      if (isNaN(userId)) {
        res.status(400).json({
          success: false,
          error: {
            code: 'INVALID_USER_ID',
            message: 'Invalid user ID',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      const success = await UserModel.deactivateAccount(userId);

      if (!success) {
        res.status(404).json({
          success: false,
          error: {
            code: 'USER_NOT_FOUND',
            message: 'User not found',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      res.json({
        success: true,
        data: {
          message: 'User deactivated successfully',
          user_id: userId
        },
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Deactivate user error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'USER_DEACTIVATION_FAILED',
          message: 'Failed to deactivate user',
          timestamp: new Date().toISOString()
        }
      });
    }
  }

  // Get all bookings with filtering
  static async getAllBookings(req: Request, res: Response): Promise<void> {
    try {
      const {
        status,
        start_date,
        end_date,
        page = 1,
        limit = 20
      } = req.query;

      let bookings;

      if (start_date && end_date) {
        bookings = await BookingModel.getByDateRange(
          start_date as string,
          end_date as string
        );
      } else {
        // For now, we'll get recent bookings
        // This could be enhanced with more filtering options
        bookings = await BookingModel.getUpcoming(undefined, undefined, parseInt(limit as string));
      }

      res.json({
        success: true,
        data: bookings,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Get all bookings error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'BOOKINGS_FETCH_FAILED',
          message: 'Failed to fetch bookings',
          timestamp: new Date().toISOString()
        }
      });
    }
  }

  // Get reviews pending moderation
  static async getPendingReviews(req: Request, res: Response): Promise<void> {
    try {
      const page = req.query.page ? parseInt(req.query.page as string) : 1;
      const limit = req.query.limit ? parseInt(req.query.limit as string) : 10;

      const result = await ReviewModel.getPendingModeration(page, limit);

      res.json({
        success: true,
        data: result.data,
        pagination: result.pagination,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Get pending reviews error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'PENDING_REVIEWS_FETCH_FAILED',
          message: 'Failed to fetch pending reviews',
          timestamp: new Date().toISOString()
        }
      });
    }
  }

  // Moderate review
  static async moderateReview(req: Request, res: Response): Promise<void> {
    try {
      const reviewId = parseInt(req.params.id);
      const { is_moderated, moderation_notes } = req.body;

      if (isNaN(reviewId)) {
        res.status(400).json({
          success: false,
          error: {
            code: 'INVALID_REVIEW_ID',
            message: 'Invalid review ID',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      const moderatedReview = await ReviewModel.moderate(reviewId, is_moderated, moderation_notes);

      if (!moderatedReview) {
        res.status(404).json({
          success: false,
          error: {
            code: 'REVIEW_NOT_FOUND',
            message: 'Review not found',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      res.json({
        success: true,
        data: moderatedReview,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Moderate review error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'REVIEW_MODERATION_FAILED',
          message: 'Failed to moderate review',
          timestamp: new Date().toISOString()
        }
      });
    }
  }

  // Get platform analytics
  static async getAnalytics(req: Request, res: Response): Promise<void> {
    try {
      const { period = '30d' } = req.query;

      // This is a simplified analytics endpoint
      // In a real implementation, you'd have more sophisticated analytics
      const analytics = {
        period,
        user_growth: {
          new_users_this_period: 0,
          growth_rate: 0
        },
        booking_trends: {
          total_bookings_this_period: 0,
          revenue_this_period: 0,
          average_booking_value: 0
        },
        popular_categories: [],
        top_guides: [],
        top_destinations: []
      };

      res.json({
        success: true,
        data: analytics,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Get analytics error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'ANALYTICS_FETCH_FAILED',
          message: 'Failed to fetch analytics',
          timestamp: new Date().toISOString()
        }
      });
    }
  }

  // System health check
  static async getSystemHealth(req: Request, res: Response): Promise<void> {
    try {
      const health = {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        services: {
          database: 'healthy',
          api: 'healthy',
          external_services: {
            payment_gateway: 'healthy',
            email_service: 'healthy',
            maps_api: 'healthy'
          }
        },
        metrics: {
          uptime: process.uptime(),
          memory_usage: process.memoryUsage(),
          cpu_usage: process.cpuUsage()
        }
      };

      res.json({
        success: true,
        data: health,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Get system health error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'SYSTEM_HEALTH_CHECK_FAILED',
          message: 'Failed to check system health',
          timestamp: new Date().toISOString()
        }
      });
    }
  }

  // Export data (simplified)
  static async exportData(req: Request, res: Response): Promise<void> {
    try {
      const { type, format = 'json' } = req.query;

      if (!type) {
        res.status(400).json({
          success: false,
          error: {
            code: 'EXPORT_TYPE_REQUIRED',
            message: 'Export type is required',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      // This is a simplified export function
      // In a real implementation, you'd generate actual export files
      const exportData = {
        type,
        format,
        generated_at: new Date().toISOString(),
        download_url: `/api/admin/exports/${type}-${Date.now()}.${format}`,
        expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 hours
      };

      res.json({
        success: true,
        data: exportData,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Export data error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'DATA_EXPORT_FAILED',
          message: 'Failed to export data',
          timestamp: new Date().toISOString()
        }
      });
    }
  }
}
