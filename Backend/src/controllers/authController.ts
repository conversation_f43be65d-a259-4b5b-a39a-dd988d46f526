import { Request, Response } from 'express';
import { UserModel } from '../models/User';
import { generateTokens, verifyRefreshToken, generateAccessToken } from '../middleware/auth';
import { CreateUserRequest, LoginRequest, ApiResponse } from '../types';

export class AuthController {
  // Register new user
  static async register(req: Request, res: Response): Promise<void> {
    try {
      const userData: CreateUserRequest = req.body;

      // Check if email already exists
      const existingUser = await UserModel.findByEmail(userData.email);
      if (existingUser) {
        res.status(409).json({
          success: false,
          error: {
            code: 'EMAIL_EXISTS',
            message: 'An account with this email already exists',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      // Create new user
      const user = await UserModel.create(userData);

      // Generate tokens
      const tokens = generateTokens({
        id: user.id,
        email: user.email,
        user_type: user.user_type
      });

      // Remove password hash from response
      const { password_hash, ...userResponse } = user;

      res.status(201).json({
        success: true,
        data: {
          user: userResponse,
          tokens
        },
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Registration error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'REGISTRATION_FAILED',
          message: 'Failed to create account',
          timestamp: new Date().toISOString()
        }
      });
    }
  }

  // Login user
  static async login(req: Request, res: Response): Promise<void> {
    try {
      const { email, password }: LoginRequest = req.body;

      // Find user by email
      const user = await UserModel.findByEmail(email);
      if (!user) {
        res.status(401).json({
          success: false,
          error: {
            code: 'INVALID_CREDENTIALS',
            message: 'Invalid email or password',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      // Verify password
      const isValidPassword = await UserModel.verifyPassword(password, user.password_hash);
      if (!isValidPassword) {
        res.status(401).json({
          success: false,
          error: {
            code: 'INVALID_CREDENTIALS',
            message: 'Invalid email or password',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      // Check if account is active
      if (!user.is_active) {
        res.status(403).json({
          success: false,
          error: {
            code: 'ACCOUNT_DEACTIVATED',
            message: 'Your account has been deactivated',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      // Generate tokens
      const tokens = generateTokens({
        id: user.id,
        email: user.email,
        user_type: user.user_type
      });

      // Remove password hash from response
      const { password_hash, ...userResponse } = user;

      res.json({
        success: true,
        data: {
          user: userResponse,
          tokens
        },
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Login error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'LOGIN_FAILED',
          message: 'Login failed',
          timestamp: new Date().toISOString()
        }
      });
    }
  }

  // Refresh access token
  static async refreshToken(req: Request, res: Response): Promise<void> {
    try {
      const { refreshToken } = req.body;

      if (!refreshToken) {
        res.status(400).json({
          success: false,
          error: {
            code: 'REFRESH_TOKEN_REQUIRED',
            message: 'Refresh token is required',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      // Verify refresh token
      const decoded = await verifyRefreshToken(refreshToken);

      // Check if user still exists and is active
      const user = await UserModel.findById(decoded.id);
      if (!user || !user.is_active) {
        res.status(401).json({
          success: false,
          error: {
            code: 'INVALID_REFRESH_TOKEN',
            message: 'Invalid refresh token',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      // Generate new access token
      const accessToken = generateAccessToken({
        id: user.id,
        email: user.email,
        user_type: user.user_type
      });

      res.json({
        success: true,
        data: {
          accessToken
        },
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Token refresh error:', error);
      res.status(401).json({
        success: false,
        error: {
          code: 'INVALID_REFRESH_TOKEN',
          message: 'Invalid refresh token',
          timestamp: new Date().toISOString()
        }
      });
    }
  }

  // Get current user profile
  static async getProfile(req: Request, res: Response): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Authentication required',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      const user = await UserModel.findById(req.user.id);
      if (!user) {
        res.status(404).json({
          success: false,
          error: {
            code: 'USER_NOT_FOUND',
            message: 'User not found',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      // Remove password hash from response
      const { password_hash, ...userResponse } = user;

      res.json({
        success: true,
        data: userResponse,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Get profile error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'PROFILE_FETCH_FAILED',
          message: 'Failed to fetch profile',
          timestamp: new Date().toISOString()
        }
      });
    }
  }

  // Update user profile
  static async updateProfile(req: Request, res: Response): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Authentication required',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      const updatedUser = await UserModel.updateProfile(req.user.id, req.body);
      if (!updatedUser) {
        res.status(404).json({
          success: false,
          error: {
            code: 'USER_NOT_FOUND',
            message: 'User not found',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      // Remove password hash from response
      const { password_hash, ...userResponse } = updatedUser;

      res.json({
        success: true,
        data: userResponse,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Update profile error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'PROFILE_UPDATE_FAILED',
          message: 'Failed to update profile',
          timestamp: new Date().toISOString()
        }
      });
    }
  }

  // Change password
  static async changePassword(req: Request, res: Response): Promise<void> {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'Authentication required',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      const { currentPassword, newPassword } = req.body;

      // Get current user
      const user = await UserModel.findById(req.user.id);
      if (!user) {
        res.status(404).json({
          success: false,
          error: {
            code: 'USER_NOT_FOUND',
            message: 'User not found',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      // Verify current password
      const isValidPassword = await UserModel.verifyPassword(currentPassword, user.password_hash);
      if (!isValidPassword) {
        res.status(400).json({
          success: false,
          error: {
            code: 'INVALID_CURRENT_PASSWORD',
            message: 'Current password is incorrect',
            timestamp: new Date().toISOString()
          }
        });
        return;
      }

      // Update password
      await UserModel.updatePassword(req.user.id, newPassword);

      res.json({
        success: true,
        data: {
          message: 'Password updated successfully'
        },
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Change password error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'PASSWORD_CHANGE_FAILED',
          message: 'Failed to change password',
          timestamp: new Date().toISOString()
        }
      });
    }
  }

  // Logout (client-side token removal)
  static async logout(req: Request, res: Response): Promise<void> {
    res.json({
      success: true,
      data: {
        message: 'Logged out successfully'
      },
      timestamp: new Date().toISOString()
    });
  }
}
