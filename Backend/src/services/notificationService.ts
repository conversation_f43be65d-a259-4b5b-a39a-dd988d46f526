import { User, Booking, Tour } from '../types';

// Email templates
export interface EmailTemplate {
  subject: string;
  html: string;
  text: string;
}

export interface NotificationData {
  user: User;
  booking?: Booking;
  tour?: Tour;
  guide?: User;
  [key: string]: any;
}

export class NotificationService {
  // Send email notification
  static async sendEmail(to: string, template: EmailTemplate): Promise<boolean> {
    try {
      // In a real implementation, you would use a service like SendGrid, Mailgun, or AWS SES
      // const nodemailer = require('nodemailer');
      // const transporter = nodemailer.createTransporter({...});
      // await transporter.sendMail({
      //   from: process.env.SMTP_USER,
      //   to,
      //   subject: template.subject,
      //   html: template.html,
      //   text: template.text
      // });

      console.log(`📧 Email sent to ${to}: ${template.subject}`);
      return true;
    } catch (error) {
      console.error('Email sending error:', error);
      return false;
    }
  }

  // Send SMS notification
  static async sendSMS(phoneNumber: string, message: string): Promise<boolean> {
    try {
      // In a real implementation, you would use a service like Twilio or AWS SNS
      console.log(`📱 SMS sent to ${phoneNumber}: ${message}`);
      return true;
    } catch (error) {
      console.error('SMS sending error:', error);
      return false;
    }
  }

  // Welcome email for new users
  static async sendWelcomeEmail(user: User): Promise<boolean> {
    const template: EmailTemplate = {
      subject: 'Welcome to DerLg - Discover Cambodia!',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #2c5aa0;">Welcome to DerLg, ${user.first_name}!</h1>
          <p>Thank you for joining DerLg, Cambodia's premier tour booking platform.</p>
          <p>As a ${user.user_type}, you can now:</p>
          ${user.user_type === 'tourist' ? `
            <ul>
              <li>Browse and book amazing tours across Cambodia</li>
              <li>Connect with verified local guides</li>
              <li>Discover cultural events and experiences</li>
              <li>Book hotels and transportation</li>
            </ul>
          ` : user.user_type === 'guide' ? `
            <ul>
              <li>Create and manage your tour offerings</li>
              <li>Connect with tourists from around the world</li>
              <li>Build your reputation through reviews</li>
              <li>Grow your tourism business</li>
            </ul>
          ` : `
            <ul>
              <li>Manage your business listings</li>
              <li>Accept bookings from tourists</li>
              <li>Provide excellent service to visitors</li>
            </ul>
          `}
          <p>Start exploring Cambodia today!</p>
          <a href="${process.env.FRONTEND_URL}" style="background-color: #2c5aa0; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px;">Explore DerLg</a>
        </div>
      `,
      text: `Welcome to DerLg, ${user.first_name}! Thank you for joining Cambodia's premier tour booking platform.`
    };

    return this.sendEmail(user.email, template);
  }

  // Booking confirmation email
  static async sendBookingConfirmation(data: NotificationData): Promise<boolean> {
    if (!data.booking || !data.tour || !data.guide) return false;

    const template: EmailTemplate = {
      subject: `Booking Confirmed - ${data.tour.title}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #28a745;">Booking Confirmed!</h1>
          <p>Dear ${data.user.first_name},</p>
          <p>Your booking has been confirmed. Here are the details:</p>
          
          <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3>${data.tour.title}</h3>
            <p><strong>Date:</strong> ${data.booking.booking_date}</p>
            <p><strong>Time:</strong> ${data.booking.booking_time || 'TBD'}</p>
            <p><strong>Participants:</strong> ${data.booking.participants}</p>
            <p><strong>Total Amount:</strong> $${data.booking.total_amount}</p>
            <p><strong>Location:</strong> ${data.tour.location}</p>
            <p><strong>Meeting Point:</strong> ${data.tour.meeting_point || 'TBD'}</p>
          </div>

          <h4>Your Guide</h4>
          <p><strong>${data.guide.first_name} ${data.guide.last_name}</strong></p>
          <p>Phone: ${data.guide.phone || 'Will be provided'}</p>

          <p>We hope you have an amazing experience exploring Cambodia!</p>
          
          <a href="${process.env.FRONTEND_URL}/bookings/${data.booking.id}" style="background-color: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px;">View Booking Details</a>
        </div>
      `,
      text: `Booking Confirmed! Your tour "${data.tour.title}" on ${data.booking.booking_date} has been confirmed.`
    };

    return this.sendEmail(data.user.email, template);
  }

  // New booking notification for guides
  static async sendNewBookingNotification(data: NotificationData): Promise<boolean> {
    if (!data.booking || !data.tour || !data.guide) return false;

    const template: EmailTemplate = {
      subject: `New Booking - ${data.tour.title}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #2c5aa0;">New Booking Received!</h1>
          <p>Dear ${data.guide.first_name},</p>
          <p>You have received a new booking for your tour:</p>
          
          <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3>${data.tour.title}</h3>
            <p><strong>Tourist:</strong> ${data.user.first_name} ${data.user.last_name}</p>
            <p><strong>Date:</strong> ${data.booking.booking_date}</p>
            <p><strong>Participants:</strong> ${data.booking.participants}</p>
            <p><strong>Total Amount:</strong> $${data.booking.total_amount}</p>
            <p><strong>Contact:</strong> ${data.user.email}</p>
            ${data.booking.special_requests ? `<p><strong>Special Requests:</strong> ${data.booking.special_requests}</p>` : ''}
          </div>

          <p>Please confirm this booking as soon as possible.</p>
          
          <a href="${process.env.FRONTEND_URL}/guide/bookings/${data.booking.id}" style="background-color: #2c5aa0; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px;">Manage Booking</a>
        </div>
      `,
      text: `New booking received for "${data.tour.title}" on ${data.booking.booking_date} by ${data.user.first_name} ${data.user.last_name}.`
    };

    return this.sendEmail(data.guide.email, template);
  }

  // Booking cancellation notification
  static async sendBookingCancellation(data: NotificationData): Promise<boolean> {
    if (!data.booking || !data.tour) return false;

    const template: EmailTemplate = {
      subject: `Booking Cancelled - ${data.tour.title}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #dc3545;">Booking Cancelled</h1>
          <p>Dear ${data.user.first_name},</p>
          <p>Your booking has been cancelled:</p>
          
          <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3>${data.tour.title}</h3>
            <p><strong>Date:</strong> ${data.booking.booking_date}</p>
            <p><strong>Participants:</strong> ${data.booking.participants}</p>
            <p><strong>Amount:</strong> $${data.booking.total_amount}</p>
            ${data.booking.cancellation_reason ? `<p><strong>Reason:</strong> ${data.booking.cancellation_reason}</p>` : ''}
          </div>

          <p>If you paid for this booking, a refund will be processed within 5-7 business days.</p>
          <p>We're sorry for any inconvenience and hope to serve you again soon.</p>
          
          <a href="${process.env.FRONTEND_URL}/tours" style="background-color: #2c5aa0; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px;">Browse Other Tours</a>
        </div>
      `,
      text: `Your booking for "${data.tour.title}" on ${data.booking.booking_date} has been cancelled.`
    };

    return this.sendEmail(data.user.email, template);
  }

  // Tour reminder notification (24 hours before)
  static async sendTourReminder(data: NotificationData): Promise<boolean> {
    if (!data.booking || !data.tour || !data.guide) return false;

    const template: EmailTemplate = {
      subject: `Tour Reminder - ${data.tour.title} Tomorrow`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #ffc107;">Tour Reminder</h1>
          <p>Dear ${data.user.first_name},</p>
          <p>This is a friendly reminder that your tour is scheduled for tomorrow:</p>
          
          <div style="background-color: #fff3cd; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #ffc107;">
            <h3>${data.tour.title}</h3>
            <p><strong>Date:</strong> ${data.booking.booking_date}</p>
            <p><strong>Time:</strong> ${data.booking.booking_time || 'TBD'}</p>
            <p><strong>Meeting Point:</strong> ${data.tour.meeting_point || 'TBD'}</p>
            <p><strong>Guide:</strong> ${data.guide.first_name} ${data.guide.last_name}</p>
            <p><strong>Guide Phone:</strong> ${data.guide.phone || 'Will be provided'}</p>
          </div>

          <h4>What to Bring:</h4>
          <ul>
            <li>Comfortable walking shoes</li>
            <li>Sun protection (hat, sunscreen)</li>
            <li>Water bottle</li>
            <li>Camera</li>
            ${data.tour.requirements ? `<li>${data.tour.requirements}</li>` : ''}
          </ul>

          <p>We're excited for your Cambodia adventure!</p>
          
          <a href="${process.env.FRONTEND_URL}/bookings/${data.booking.id}" style="background-color: #ffc107; color: black; padding: 12px 24px; text-decoration: none; border-radius: 4px;">View Booking Details</a>
        </div>
      `,
      text: `Reminder: Your tour "${data.tour.title}" is scheduled for tomorrow at ${data.booking.booking_time || 'TBD'}.`
    };

    // Send both email and SMS reminder
    const emailSent = await this.sendEmail(data.user.email, template);
    let smsSent = false;

    if (data.user.phone) {
      const smsMessage = `Reminder: Your tour "${data.tour.title}" is tomorrow at ${data.booking.booking_time || 'TBD'}. Meeting point: ${data.tour.meeting_point || 'TBD'}. Guide: ${data.guide.first_name} ${data.guide.phone || ''}`;
      smsSent = await this.sendSMS(data.user.phone, smsMessage);
    }

    return emailSent || smsSent;
  }

  // Review request notification
  static async sendReviewRequest(data: NotificationData): Promise<boolean> {
    if (!data.booking || !data.tour) return false;

    const template: EmailTemplate = {
      subject: `How was your tour? - ${data.tour.title}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #2c5aa0;">How was your Cambodia experience?</h1>
          <p>Dear ${data.user.first_name},</p>
          <p>We hope you had an amazing time on your recent tour:</p>
          
          <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3>${data.tour.title}</h3>
            <p><strong>Date:</strong> ${data.booking.booking_date}</p>
            <p><strong>Location:</strong> ${data.tour.location}</p>
          </div>

          <p>Your feedback helps other travelers and supports our local guides. Would you mind sharing your experience?</p>
          
          <a href="${process.env.FRONTEND_URL}/bookings/${data.booking.id}/review" style="background-color: #2c5aa0; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px;">Write a Review</a>
          
          <p style="margin-top: 30px;">Thank you for choosing DerLg!</p>
        </div>
      `,
      text: `How was your tour "${data.tour.title}"? Please share your experience by writing a review.`
    };

    return this.sendEmail(data.user.email, template);
  }

  // Password reset notification
  static async sendPasswordReset(user: User, resetToken: string): Promise<boolean> {
    const resetUrl = `${process.env.FRONTEND_URL}/reset-password?token=${resetToken}`;
    
    const template: EmailTemplate = {
      subject: 'Reset Your DerLg Password',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #2c5aa0;">Reset Your Password</h1>
          <p>Dear ${user.first_name},</p>
          <p>You requested to reset your password for your DerLg account.</p>
          <p>Click the button below to reset your password:</p>
          
          <a href="${resetUrl}" style="background-color: #2c5aa0; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px;">Reset Password</a>
          
          <p style="margin-top: 20px;">This link will expire in 1 hour for security reasons.</p>
          <p>If you didn't request this password reset, please ignore this email.</p>
        </div>
      `,
      text: `Reset your DerLg password by clicking this link: ${resetUrl}`
    };

    return this.sendEmail(user.email, template);
  }

  // Account verification notification
  static async sendAccountVerification(user: User, verificationToken: string): Promise<boolean> {
    const verificationUrl = `${process.env.FRONTEND_URL}/verify-account?token=${verificationToken}`;
    
    const template: EmailTemplate = {
      subject: 'Verify Your DerLg Account',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #2c5aa0;">Verify Your Account</h1>
          <p>Dear ${user.first_name},</p>
          <p>Thank you for registering with DerLg! Please verify your email address to complete your account setup.</p>
          
          <a href="${verificationUrl}" style="background-color: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px;">Verify Account</a>
          
          <p style="margin-top: 20px;">Once verified, you'll have full access to all DerLg features.</p>
        </div>
      `,
      text: `Verify your DerLg account by clicking this link: ${verificationUrl}`
    };

    return this.sendEmail(user.email, template);
  }
}
