import { Request, Response, NextFunction } from 'express';
import <PERSON><PERSON> from 'joi';

// Generic validation middleware
export const validate = (schema: Joi.ObjectSchema) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const { error, value } = schema.validate(req.body, {
      abortEarly: false,
      stripUnknown: true
    });

    if (error) {
      const details = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        value: detail.context?.value
      }));

      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid input data',
          details,
          timestamp: new Date().toISOString()
        }
      });
    }

    req.body = value;
    next();
  };
};

// User validation schemas
export const registerSchema = Joi.object({
  email: Joi.string().email().required().messages({
    'string.email': 'Please provide a valid email address',
    'any.required': 'Email is required'
  }),
  password: Joi.string().min(8).pattern(new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)')).required().messages({
    'string.min': 'Password must be at least 8 characters long',
    'string.pattern.base': 'Password must contain at least one uppercase letter, one lowercase letter, and one number',
    'any.required': 'Password is required'
  }),
  first_name: Joi.string().min(2).max(50).required().messages({
    'string.min': 'First name must be at least 2 characters long',
    'string.max': 'First name cannot exceed 50 characters',
    'any.required': 'First name is required'
  }),
  last_name: Joi.string().min(2).max(50).required().messages({
    'string.min': 'Last name must be at least 2 characters long',
    'string.max': 'Last name cannot exceed 50 characters',
    'any.required': 'Last name is required'
  }),
  phone: Joi.string().pattern(new RegExp('^\\+?[1-9]\\d{1,14}$')).optional().messages({
    'string.pattern.base': 'Please provide a valid phone number'
  }),
  user_type: Joi.string().valid('tourist', 'guide', 'server').default('tourist'),
  preferred_language: Joi.string().valid('en', 'km', 'zh').default('en')
});

export const loginSchema = Joi.object({
  email: Joi.string().email().required().messages({
    'string.email': 'Please provide a valid email address',
    'any.required': 'Email is required'
  }),
  password: Joi.string().required().messages({
    'any.required': 'Password is required'
  })
});

// Tour validation schemas
export const createTourSchema = Joi.object({
  title: Joi.string().min(5).max(255).required().messages({
    'string.min': 'Tour title must be at least 5 characters long',
    'string.max': 'Tour title cannot exceed 255 characters',
    'any.required': 'Tour title is required'
  }),
  description: Joi.string().min(50).required().messages({
    'string.min': 'Tour description must be at least 50 characters long',
    'any.required': 'Tour description is required'
  }),
  duration_hours: Joi.number().integer().min(1).max(168).required().messages({
    'number.min': 'Duration must be at least 1 hour',
    'number.max': 'Duration cannot exceed 168 hours (1 week)',
    'any.required': 'Duration is required'
  }),
  max_participants: Joi.number().integer().min(1).max(50).required().messages({
    'number.min': 'Must allow at least 1 participant',
    'number.max': 'Cannot exceed 50 participants',
    'any.required': 'Maximum participants is required'
  }),
  price_usd: Joi.number().positive().precision(2).required().messages({
    'number.positive': 'Price must be a positive number',
    'any.required': 'Price is required'
  }),
  location: Joi.string().min(3).max(255).required().messages({
    'string.min': 'Location must be at least 3 characters long',
    'any.required': 'Location is required'
  }),
  latitude: Joi.number().min(-90).max(90).optional(),
  longitude: Joi.number().min(-180).max(180).optional(),
  category: Joi.string().valid('cultural', 'adventure', 'nature', 'food', 'historical', 'religious', 'shopping').required(),
  difficulty_level: Joi.string().valid('easy', 'moderate', 'challenging').default('easy'),
  includes: Joi.string().optional(),
  excludes: Joi.string().optional(),
  requirements: Joi.string().optional(),
  cancellation_policy: Joi.string().optional(),
  meeting_point: Joi.string().optional(),
  meeting_point_lat: Joi.number().min(-90).max(90).optional(),
  meeting_point_lng: Joi.number().min(-180).max(180).optional(),
  cultural_significance: Joi.string().optional(),
  dress_code: Joi.string().optional()
});

// Booking validation schemas
export const createBookingSchema = Joi.object({
  tour_id: Joi.number().integer().positive().required().messages({
    'number.positive': 'Tour ID must be a positive number',
    'any.required': 'Tour ID is required'
  }),
  booking_date: Joi.date().min('now').required().messages({
    'date.min': 'Booking date cannot be in the past',
    'any.required': 'Booking date is required'
  }),
  booking_time: Joi.string().pattern(new RegExp('^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$')).optional().messages({
    'string.pattern.base': 'Booking time must be in HH:MM format'
  }),
  participants: Joi.number().integer().min(1).max(50).required().messages({
    'number.min': 'Must have at least 1 participant',
    'number.max': 'Cannot exceed 50 participants',
    'any.required': 'Number of participants is required'
  }),
  special_requests: Joi.string().max(1000).optional(),
  emergency_contact_name: Joi.string().min(2).max(100).optional(),
  emergency_contact_phone: Joi.string().pattern(new RegExp('^\\+?[1-9]\\d{1,14}$')).optional()
});

// Hotel validation schemas
export const createHotelSchema = Joi.object({
  name: Joi.string().min(3).max(255).required(),
  description: Joi.string().optional(),
  address: Joi.string().min(10).max(255).required(),
  latitude: Joi.number().min(-90).max(90).optional(),
  longitude: Joi.number().min(-180).max(180).optional(),
  star_rating: Joi.number().integer().min(1).max(5).optional(),
  price_per_night: Joi.number().positive().precision(2).required(),
  amenities: Joi.array().items(Joi.string()).optional(),
  contact_phone: Joi.string().pattern(new RegExp('^\\+?[1-9]\\d{1,14}$')).optional(),
  contact_email: Joi.string().email().optional()
});

// Event validation schemas
export const createEventSchema = Joi.object({
  title: Joi.string().min(5).max(255).required(),
  description: Joi.string().min(20).required(),
  event_type: Joi.string().valid('festival', 'market', 'ceremony', 'performance', 'workshop', 'exhibition').required(),
  location: Joi.string().min(3).max(255).required(),
  latitude: Joi.number().min(-90).max(90).optional(),
  longitude: Joi.number().min(-180).max(180).optional(),
  start_date: Joi.date().min('now').required(),
  end_date: Joi.date().min(Joi.ref('start_date')).optional(),
  start_time: Joi.string().pattern(new RegExp('^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$')).optional(),
  end_time: Joi.string().pattern(new RegExp('^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$')).optional(),
  entry_fee: Joi.number().min(0).precision(2).default(0),
  cultural_significance: Joi.string().optional(),
  dress_code: Joi.string().optional(),
  visitor_guidelines: Joi.string().optional()
});

// Review validation schemas
export const createReviewSchema = Joi.object({
  booking_id: Joi.number().integer().positive().required(),
  rating: Joi.number().integer().min(1).max(5).required(),
  comment: Joi.string().min(10).max(2000).optional(),
  guide_rating: Joi.number().integer().min(1).max(5).optional(),
  tour_rating: Joi.number().integer().min(1).max(5).optional(),
  value_rating: Joi.number().integer().min(1).max(5).optional(),
  safety_rating: Joi.number().integer().min(1).max(5).optional()
});

// Query parameter validation
export const paginationSchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(10),
  sort_by: Joi.string().optional(),
  sort_order: Joi.string().valid('asc', 'desc').default('desc')
});

// Validate query parameters
export const validateQuery = (schema: Joi.ObjectSchema) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const { error, value } = schema.validate(req.query, {
      abortEarly: false,
      stripUnknown: true
    });

    if (error) {
      const details = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        value: detail.context?.value
      }));

      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid query parameters',
          details,
          timestamp: new Date().toISOString()
        }
      });
    }

    req.query = value;
    next();
  };
};
