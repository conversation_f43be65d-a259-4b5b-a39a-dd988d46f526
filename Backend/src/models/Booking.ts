import { executeQuery } from '../config/database';
import { Booking, CreateBookingRequest, PaginatedResponse } from '../types';

export class BookingModel {
  // Create a new booking
  static async create(userId: number, bookingData: CreateBookingRequest): Promise<Booking> {
    const {
      tour_id,
      booking_date,
      booking_time,
      participants,
      special_requests,
      emergency_contact_name,
      emergency_contact_phone
    } = bookingData;

    // Get tour details to calculate total amount
    const tourQuery = 'SELECT price_usd FROM tours WHERE id = ? AND is_active = TRUE';
    const tourResult = await executeQuery(tourQuery, [tour_id]);
    
    if (tourResult.length === 0) {
      throw new Error('Tour not found');
    }

    const total_amount = tourResult[0].price_usd * participants;

    const query = `
      INSERT INTO bookings (
        user_id, tour_id, booking_date, booking_time, participants,
        total_amount, special_requests, emergency_contact_name, emergency_contact_phone
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const result = await executeQuery(query, [
      userId, tour_id, booking_date, booking_time, participants,
      total_amount, special_requests, emergency_contact_name, emergency_contact_phone
    ]);

    return this.findById(result.insertId);
  }

  // Find booking by ID with tour and user details
  static async findById(id: number): Promise<Booking | null> {
    const query = `
      SELECT b.*, 
             t.title as tour_title,
             t.description as tour_description,
             t.location as tour_location,
             t.duration_hours as tour_duration,
             t.meeting_point as tour_meeting_point,
             t.guide_id as tour_guide_id,
             u.first_name as user_first_name,
             u.last_name as user_last_name,
             u.email as user_email,
             u.phone as user_phone,
             g.first_name as guide_first_name,
             g.last_name as guide_last_name,
             g.phone as guide_phone
      FROM bookings b
      LEFT JOIN tours t ON b.tour_id = t.id
      LEFT JOIN users u ON b.user_id = u.id
      LEFT JOIN users g ON t.guide_id = g.id
      WHERE b.id = ?
    `;
    
    const results = await executeQuery(query, [id]);
    return results.length > 0 ? results[0] : null;
  }

  // Get bookings by user
  static async getByUser(userId: number, page: number = 1, limit: number = 10): Promise<PaginatedResponse<Booking>> {
    const offset = (page - 1) * limit;

    const countQuery = `
      SELECT COUNT(*) as total 
      FROM bookings 
      WHERE user_id = ?
    `;

    const dataQuery = `
      SELECT b.*, 
             t.title as tour_title,
             t.location as tour_location,
             t.duration_hours as tour_duration,
             t.guide_id as tour_guide_id,
             g.first_name as guide_first_name,
             g.last_name as guide_last_name
      FROM bookings b
      LEFT JOIN tours t ON b.tour_id = t.id
      LEFT JOIN users g ON t.guide_id = g.id
      WHERE b.user_id = ?
      ORDER BY b.created_at DESC
      LIMIT ? OFFSET ?
    `;

    const [countResult, bookings] = await Promise.all([
      executeQuery(countQuery, [userId]),
      executeQuery(dataQuery, [userId, limit, offset])
    ]);

    const total = countResult[0].total;
    const totalPages = Math.ceil(total / limit);

    return {
      data: bookings,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    };
  }

  // Get bookings by guide
  static async getByGuide(guideId: number, page: number = 1, limit: number = 10): Promise<PaginatedResponse<Booking>> {
    const offset = (page - 1) * limit;

    const countQuery = `
      SELECT COUNT(*) as total 
      FROM bookings b
      JOIN tours t ON b.tour_id = t.id
      WHERE t.guide_id = ?
    `;

    const dataQuery = `
      SELECT b.*, 
             t.title as tour_title,
             t.location as tour_location,
             t.duration_hours as tour_duration,
             u.first_name as user_first_name,
             u.last_name as user_last_name,
             u.phone as user_phone
      FROM bookings b
      JOIN tours t ON b.tour_id = t.id
      LEFT JOIN users u ON b.user_id = u.id
      WHERE t.guide_id = ?
      ORDER BY b.booking_date DESC, b.created_at DESC
      LIMIT ? OFFSET ?
    `;

    const [countResult, bookings] = await Promise.all([
      executeQuery(countQuery, [guideId]),
      executeQuery(dataQuery, [guideId, limit, offset])
    ]);

    const total = countResult[0].total;
    const totalPages = Math.ceil(total / limit);

    return {
      data: bookings,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    };
  }

  // Update booking status
  static async updateStatus(id: number, status: string, userId?: number): Promise<Booking | null> {
    const validStatuses = ['pending', 'confirmed', 'cancelled', 'completed', 'refunded'];
    
    if (!validStatuses.includes(status)) {
      throw new Error('Invalid booking status');
    }

    let query = `
      UPDATE bookings 
      SET status = ?, updated_at = CURRENT_TIMESTAMP 
      WHERE id = ?
    `;
    let params = [status, id];

    // If userId is provided, ensure user owns the booking
    if (userId) {
      query += ' AND user_id = ?';
      params.push(userId);
    }

    const result = await executeQuery(query, params);
    
    if (result.affectedRows === 0) {
      return null;
    }

    return this.findById(id);
  }

  // Update payment status
  static async updatePaymentStatus(id: number, paymentStatus: string, paymentId?: string): Promise<boolean> {
    const validPaymentStatuses = ['pending', 'paid', 'failed', 'refunded'];
    
    if (!validPaymentStatuses.includes(paymentStatus)) {
      throw new Error('Invalid payment status');
    }

    const query = `
      UPDATE bookings 
      SET payment_status = ?, payment_id = ?, updated_at = CURRENT_TIMESTAMP 
      WHERE id = ?
    `;

    const result = await executeQuery(query, [paymentStatus, paymentId, id]);
    return result.affectedRows > 0;
  }

  // Cancel booking
  static async cancel(id: number, userId: number, reason?: string): Promise<Booking | null> {
    const query = `
      UPDATE bookings 
      SET status = 'cancelled', cancellation_reason = ?, updated_at = CURRENT_TIMESTAMP 
      WHERE id = ? AND user_id = ? AND status IN ('pending', 'confirmed')
    `;

    const result = await executeQuery(query, [reason, id, userId]);
    
    if (result.affectedRows === 0) {
      return null;
    }

    return this.findById(id);
  }

  // Confirm booking (Guide only)
  static async confirm(id: number, guideId: number): Promise<Booking | null> {
    const query = `
      UPDATE bookings b
      JOIN tours t ON b.tour_id = t.id
      SET b.status = 'confirmed', b.updated_at = CURRENT_TIMESTAMP 
      WHERE b.id = ? AND t.guide_id = ? AND b.status = 'pending'
    `;

    const result = await executeQuery(query, [id, guideId]);
    
    if (result.affectedRows === 0) {
      return null;
    }

    return this.findById(id);
  }

  // Complete booking
  static async complete(id: number): Promise<Booking | null> {
    const query = `
      UPDATE bookings 
      SET status = 'completed', updated_at = CURRENT_TIMESTAMP 
      WHERE id = ? AND status = 'confirmed' AND booking_date <= CURDATE()
    `;

    const result = await executeQuery(query, [id]);
    
    if (result.affectedRows === 0) {
      return null;
    }

    // Update tour booking count
    const booking = await this.findById(id);
    if (booking) {
      await executeQuery(
        'UPDATE tours SET total_bookings = total_bookings + 1 WHERE id = ?',
        [booking.tour_id]
      );
    }

    return booking;
  }

  // Get booking statistics
  static async getStats(): Promise<any> {
    const query = `
      SELECT 
        COUNT(*) as total_bookings,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_bookings,
        COUNT(CASE WHEN status = 'confirmed' THEN 1 END) as confirmed_bookings,
        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_bookings,
        COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_bookings,
        SUM(CASE WHEN payment_status = 'paid' THEN total_amount ELSE 0 END) as total_revenue,
        AVG(total_amount) as average_booking_value,
        SUM(participants) as total_participants
      FROM bookings
    `;

    const result = await executeQuery(query);
    return result[0];
  }

  // Get upcoming bookings
  static async getUpcoming(userId?: number, guideId?: number, limit: number = 10): Promise<Booking[]> {
    let query = `
      SELECT b.*, 
             t.title as tour_title,
             t.location as tour_location,
             t.meeting_point as tour_meeting_point,
             u.first_name as user_first_name,
             u.last_name as user_last_name,
             g.first_name as guide_first_name,
             g.last_name as guide_last_name
      FROM bookings b
      LEFT JOIN tours t ON b.tour_id = t.id
      LEFT JOIN users u ON b.user_id = u.id
      LEFT JOIN users g ON t.guide_id = g.id
      WHERE b.booking_date >= CURDATE() AND b.status IN ('confirmed', 'pending')
    `;
    
    const params: any[] = [];

    if (userId) {
      query += ' AND b.user_id = ?';
      params.push(userId);
    }

    if (guideId) {
      query += ' AND t.guide_id = ?';
      params.push(guideId);
    }

    query += ' ORDER BY b.booking_date ASC, b.booking_time ASC LIMIT ?';
    params.push(limit);

    return executeQuery(query, params);
  }

  // Check if user can review booking
  static async canReview(bookingId: number, userId: number): Promise<boolean> {
    const query = `
      SELECT COUNT(*) as count
      FROM bookings b
      LEFT JOIN reviews r ON b.id = r.booking_id
      WHERE b.id = ? AND b.user_id = ? AND b.status = 'completed' AND r.id IS NULL
    `;

    const result = await executeQuery(query, [bookingId, userId]);
    return result[0].count > 0;
  }

  // Get bookings by date range
  static async getByDateRange(startDate: string, endDate: string, guideId?: number): Promise<Booking[]> {
    let query = `
      SELECT b.*, 
             t.title as tour_title,
             t.location as tour_location,
             u.first_name as user_first_name,
             u.last_name as user_last_name
      FROM bookings b
      LEFT JOIN tours t ON b.tour_id = t.id
      LEFT JOIN users u ON b.user_id = u.id
      WHERE b.booking_date BETWEEN ? AND ? AND b.status IN ('confirmed', 'pending')
    `;
    
    const params = [startDate, endDate];

    if (guideId) {
      query += ' AND t.guide_id = ?';
      params.push(guideId);
    }

    query += ' ORDER BY b.booking_date ASC, b.booking_time ASC';

    return executeQuery(query, params);
  }
}
