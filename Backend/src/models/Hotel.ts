import { executeQuery } from '../config/database';
import { Hotel, PaginatedResponse } from '../types';

export interface CreateHotelRequest {
  name: string;
  description?: string;
  address: string;
  latitude?: number;
  longitude?: number;
  star_rating?: number;
  price_per_night: number;
  amenities?: string[];
  room_types?: any[];
  images?: string[];
  contact_phone?: string;
  contact_email?: string;
  check_in_time?: string;
  check_out_time?: string;
  cancellation_policy?: string;
}

export interface HotelSearchFilters {
  location?: string;
  price_min?: number;
  price_max?: number;
  star_rating?: number;
  amenities?: string[];
  check_in_date?: string;
  check_out_date?: string;
  guests?: number;
  page?: number;
  limit?: number;
  sort_by?: 'price_per_night' | 'average_rating' | 'star_rating' | 'created_at';
  sort_order?: 'asc' | 'desc';
}

export class HotelModel {
  // Create a new hotel
  static async create(ownerId: number, hotelData: CreateHotelRequest): Promise<Hotel> {
    const {
      name,
      description,
      address,
      latitude,
      longitude,
      star_rating,
      price_per_night,
      amenities,
      room_types,
      images,
      contact_phone,
      contact_email,
      check_in_time = '14:00:00',
      check_out_time = '11:00:00',
      cancellation_policy
    } = hotelData;

    const query = `
      INSERT INTO hotels (
        owner_id, name, description, address, latitude, longitude,
        star_rating, price_per_night, amenities, room_types, images,
        contact_phone, contact_email, check_in_time, check_out_time,
        cancellation_policy
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const result = await executeQuery(query, [
      ownerId, name, description, address, latitude, longitude,
      star_rating, price_per_night,
      amenities ? JSON.stringify(amenities) : null,
      room_types ? JSON.stringify(room_types) : null,
      images ? JSON.stringify(images) : null,
      contact_phone, contact_email, check_in_time, check_out_time,
      cancellation_policy
    ]);

    return this.findById(result.insertId);
  }

  // Find hotel by ID
  static async findById(id: number): Promise<Hotel | null> {
    const query = `
      SELECT h.*, 
             u.first_name as owner_first_name,
             u.last_name as owner_last_name,
             u.phone as owner_phone
      FROM hotels h
      LEFT JOIN users u ON h.owner_id = u.id
      WHERE h.id = ? AND h.is_active = TRUE
    `;
    
    const results = await executeQuery(query, [id]);
    return results.length > 0 ? results[0] : null;
  }

  // Search and filter hotels
  static async search(filters: HotelSearchFilters): Promise<PaginatedResponse<Hotel>> {
    const {
      location,
      price_min,
      price_max,
      star_rating,
      amenities,
      check_in_date,
      check_out_date,
      guests,
      page = 1,
      limit = 10,
      sort_by = 'created_at',
      sort_order = 'desc'
    } = filters;

    const offset = (page - 1) * limit;
    let whereConditions = ['h.is_active = TRUE'];
    let queryParams: any[] = [];

    // Build WHERE conditions
    if (location) {
      whereConditions.push('(h.name LIKE ? OR h.address LIKE ?)');
      queryParams.push(`%${location}%`, `%${location}%`);
    }

    if (price_min !== undefined) {
      whereConditions.push('h.price_per_night >= ?');
      queryParams.push(price_min);
    }

    if (price_max !== undefined) {
      whereConditions.push('h.price_per_night <= ?');
      queryParams.push(price_max);
    }

    if (star_rating !== undefined) {
      whereConditions.push('h.star_rating >= ?');
      queryParams.push(star_rating);
    }

    // Check availability if dates provided
    if (check_in_date && check_out_date) {
      whereConditions.push(`
        h.id NOT IN (
          SELECT DISTINCT hotel_id 
          FROM hotel_bookings 
          WHERE status IN ('confirmed', 'pending') 
          AND (
            (check_in_date <= ? AND check_out_date > ?) OR
            (check_in_date < ? AND check_out_date >= ?) OR
            (check_in_date >= ? AND check_out_date <= ?)
          )
        )
      `);
      queryParams.push(
        check_in_date, check_in_date,
        check_out_date, check_out_date,
        check_in_date, check_out_date
      );
    }

    const whereClause = whereConditions.join(' AND ');

    // Validate sort_by to prevent SQL injection
    const allowedSortFields = ['price_per_night', 'average_rating', 'star_rating', 'created_at'];
    const sortField = allowedSortFields.includes(sort_by) ? sort_by : 'created_at';
    const sortDirection = sort_order === 'asc' ? 'ASC' : 'DESC';

    // Count query
    const countQuery = `
      SELECT COUNT(*) as total 
      FROM hotels h 
      WHERE ${whereClause}
    `;

    // Data query
    const dataQuery = `
      SELECT h.*, 
             u.first_name as owner_first_name,
             u.last_name as owner_last_name
      FROM hotels h
      LEFT JOIN users u ON h.owner_id = u.id
      WHERE ${whereClause}
      ORDER BY h.${sortField} ${sortDirection}
      LIMIT ? OFFSET ?
    `;

    const [countResult, hotels] = await Promise.all([
      executeQuery(countQuery, queryParams),
      executeQuery(dataQuery, [...queryParams, limit, offset])
    ]);

    const total = countResult[0].total;
    const totalPages = Math.ceil(total / limit);

    return {
      data: hotels,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    };
  }

  // Get hotels by owner
  static async getByOwner(ownerId: number, page: number = 1, limit: number = 10): Promise<PaginatedResponse<Hotel>> {
    const offset = (page - 1) * limit;

    const countQuery = `
      SELECT COUNT(*) as total 
      FROM hotels 
      WHERE owner_id = ? AND is_active = TRUE
    `;

    const dataQuery = `
      SELECT * FROM hotels 
      WHERE owner_id = ? AND is_active = TRUE
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `;

    const [countResult, hotels] = await Promise.all([
      executeQuery(countQuery, [ownerId]),
      executeQuery(dataQuery, [ownerId, limit, offset])
    ]);

    const total = countResult[0].total;
    const totalPages = Math.ceil(total / limit);

    return {
      data: hotels,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    };
  }

  // Update hotel
  static async update(id: number, ownerId: number, updateData: Partial<CreateHotelRequest>): Promise<Hotel | null> {
    const allowedFields = [
      'name', 'description', 'address', 'latitude', 'longitude',
      'star_rating', 'price_per_night', 'amenities', 'room_types',
      'images', 'contact_phone', 'contact_email', 'check_in_time',
      'check_out_time', 'cancellation_policy'
    ];

    const updateFields = Object.keys(updateData)
      .filter(key => allowedFields.includes(key))
      .map(key => `${key} = ?`);

    if (updateFields.length === 0) {
      throw new Error('No valid fields to update');
    }

    const values = Object.keys(updateData)
      .filter(key => allowedFields.includes(key))
      .map(key => {
        const value = updateData[key as keyof CreateHotelRequest];
        return Array.isArray(value) ? JSON.stringify(value) : value;
      });

    const query = `
      UPDATE hotels 
      SET ${updateFields.join(', ')}, updated_at = CURRENT_TIMESTAMP 
      WHERE id = ? AND owner_id = ? AND is_active = TRUE
    `;

    const result = await executeQuery(query, [...values, id, ownerId]);
    
    if (result.affectedRows === 0) {
      return null;
    }

    return this.findById(id);
  }

  // Delete hotel (soft delete)
  static async delete(id: number, ownerId: number): Promise<boolean> {
    const query = `
      UPDATE hotels 
      SET is_active = FALSE, updated_at = CURRENT_TIMESTAMP 
      WHERE id = ? AND owner_id = ? AND is_active = TRUE
    `;

    const result = await executeQuery(query, [id, ownerId]);
    return result.affectedRows > 0;
  }

  // Get featured hotels
  static async getFeatured(limit: number = 6): Promise<Hotel[]> {
    const query = `
      SELECT h.*, 
             u.first_name as owner_first_name,
             u.last_name as owner_last_name
      FROM hotels h
      LEFT JOIN users u ON h.owner_id = u.id
      WHERE h.is_active = TRUE AND h.average_rating >= 4.0
      ORDER BY h.average_rating DESC, h.total_reviews DESC
      LIMIT ?
    `;

    return executeQuery(query, [limit]);
  }

  // Update hotel rating
  static async updateRating(hotelId: number): Promise<void> {
    // Note: This would be implemented when hotel reviews are added
    // For now, we'll just update based on hotel_bookings feedback if available
    const query = `
      UPDATE hotels 
      SET 
        total_bookings = (
          SELECT COUNT(*) 
          FROM hotel_bookings 
          WHERE hotel_id = ? AND status = 'completed'
        )
      WHERE id = ?
    `;

    await executeQuery(query, [hotelId, hotelId]);
  }

  // Get hotel statistics
  static async getStats(): Promise<any> {
    const query = `
      SELECT 
        COUNT(*) as total_hotels,
        COUNT(CASE WHEN is_active = TRUE THEN 1 END) as active_hotels,
        AVG(price_per_night) as average_price,
        AVG(average_rating) as average_rating,
        SUM(total_bookings) as total_bookings,
        AVG(star_rating) as average_star_rating
      FROM hotels
    `;

    const result = await executeQuery(query);
    return result[0];
  }

  // Check hotel availability
  static async checkAvailability(hotelId: number, checkInDate: string, checkOutDate: string, guests: number = 1): Promise<boolean> {
    const query = `
      SELECT h.id
      FROM hotels h
      WHERE h.id = ? AND h.is_active = TRUE
      AND h.id NOT IN (
        SELECT DISTINCT hotel_id 
        FROM hotel_bookings 
        WHERE status IN ('confirmed', 'pending') 
        AND (
          (check_in_date <= ? AND check_out_date > ?) OR
          (check_in_date < ? AND check_out_date >= ?) OR
          (check_in_date >= ? AND check_out_date <= ?)
        )
      )
    `;

    const result = await executeQuery(query, [
      hotelId,
      checkInDate, checkInDate,
      checkOutDate, checkOutDate,
      checkInDate, checkOutDate
    ]);

    return result.length > 0;
  }

  // Get nearby hotels
  static async getNearby(latitude: number, longitude: number, radiusKm: number = 10, limit: number = 10): Promise<Hotel[]> {
    const query = `
      SELECT h.*, 
             u.first_name as owner_first_name,
             u.last_name as owner_last_name,
             (6371 * acos(cos(radians(?)) * cos(radians(h.latitude)) * 
              cos(radians(h.longitude) - radians(?)) + 
              sin(radians(?)) * sin(radians(h.latitude)))) AS distance
      FROM hotels h
      LEFT JOIN users u ON h.owner_id = u.id
      WHERE h.is_active = TRUE 
      AND h.latitude IS NOT NULL 
      AND h.longitude IS NOT NULL
      HAVING distance <= ?
      ORDER BY distance ASC
      LIMIT ?
    `;

    return executeQuery(query, [latitude, longitude, latitude, radiusKm, limit]);
  }
}
