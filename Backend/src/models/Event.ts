import { executeQuery } from '../config/database';
import { Event, PaginatedResponse } from '../types';

export interface CreateEventRequest {
  title: string;
  description: string;
  event_type: 'festival' | 'market' | 'ceremony' | 'performance' | 'workshop' | 'exhibition';
  location: string;
  latitude?: number;
  longitude?: number;
  start_date: string;
  end_date?: string;
  start_time?: string;
  end_time?: string;
  is_recurring?: boolean;
  recurrence_pattern?: any;
  entry_fee?: number;
  cultural_significance?: string;
  dress_code?: string;
  visitor_guidelines?: string;
  contact_info?: any;
  images?: string[];
}

export interface EventSearchFilters {
  event_type?: string;
  location?: string;
  start_date?: string;
  end_date?: string;
  entry_fee_max?: number;
  page?: number;
  limit?: number;
  sort_by?: 'start_date' | 'entry_fee' | 'created_at';
  sort_order?: 'asc' | 'desc';
}

export class EventModel {
  // Create a new event
  static async create(createdBy: number, eventData: CreateEventRequest): Promise<Event> {
    const {
      title,
      description,
      event_type,
      location,
      latitude,
      longitude,
      start_date,
      end_date,
      start_time,
      end_time,
      is_recurring = false,
      recurrence_pattern,
      entry_fee = 0,
      cultural_significance,
      dress_code,
      visitor_guidelines,
      contact_info,
      images
    } = eventData;

    const query = `
      INSERT INTO events (
        title, description, event_type, location, latitude, longitude,
        start_date, end_date, start_time, end_time, is_recurring,
        recurrence_pattern, entry_fee, cultural_significance, dress_code,
        visitor_guidelines, contact_info, images, created_by
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const result = await executeQuery(query, [
      title, description, event_type, location, latitude, longitude,
      start_date, end_date, start_time, end_time, is_recurring,
      recurrence_pattern ? JSON.stringify(recurrence_pattern) : null,
      entry_fee, cultural_significance, dress_code, visitor_guidelines,
      contact_info ? JSON.stringify(contact_info) : null,
      images ? JSON.stringify(images) : null,
      createdBy
    ]);

    return this.findById(result.insertId);
  }

  // Find event by ID
  static async findById(id: number): Promise<Event | null> {
    const query = `
      SELECT e.*, 
             u.first_name as creator_first_name,
             u.last_name as creator_last_name
      FROM events e
      LEFT JOIN users u ON e.created_by = u.id
      WHERE e.id = ? AND e.is_active = TRUE
    `;
    
    const results = await executeQuery(query, [id]);
    return results.length > 0 ? results[0] : null;
  }

  // Search and filter events
  static async search(filters: EventSearchFilters): Promise<PaginatedResponse<Event>> {
    const {
      event_type,
      location,
      start_date,
      end_date,
      entry_fee_max,
      page = 1,
      limit = 10,
      sort_by = 'start_date',
      sort_order = 'asc'
    } = filters;

    const offset = (page - 1) * limit;
    let whereConditions = ['e.is_active = TRUE'];
    let queryParams: any[] = [];

    // Build WHERE conditions
    if (event_type) {
      whereConditions.push('e.event_type = ?');
      queryParams.push(event_type);
    }

    if (location) {
      whereConditions.push('e.location LIKE ?');
      queryParams.push(`%${location}%`);
    }

    if (start_date) {
      whereConditions.push('e.start_date >= ?');
      queryParams.push(start_date);
    }

    if (end_date) {
      whereConditions.push('(e.end_date IS NULL OR e.end_date <= ?)');
      queryParams.push(end_date);
    }

    if (entry_fee_max !== undefined) {
      whereConditions.push('e.entry_fee <= ?');
      queryParams.push(entry_fee_max);
    }

    const whereClause = whereConditions.join(' AND ');

    // Validate sort_by to prevent SQL injection
    const allowedSortFields = ['start_date', 'entry_fee', 'created_at'];
    const sortField = allowedSortFields.includes(sort_by) ? sort_by : 'start_date';
    const sortDirection = sort_order === 'asc' ? 'ASC' : 'DESC';

    // Count query
    const countQuery = `
      SELECT COUNT(*) as total 
      FROM events e 
      WHERE ${whereClause}
    `;

    // Data query
    const dataQuery = `
      SELECT e.*, 
             u.first_name as creator_first_name,
             u.last_name as creator_last_name
      FROM events e
      LEFT JOIN users u ON e.created_by = u.id
      WHERE ${whereClause}
      ORDER BY e.${sortField} ${sortDirection}
      LIMIT ? OFFSET ?
    `;

    const [countResult, events] = await Promise.all([
      executeQuery(countQuery, queryParams),
      executeQuery(dataQuery, [...queryParams, limit, offset])
    ]);

    const total = countResult[0].total;
    const totalPages = Math.ceil(total / limit);

    return {
      data: events,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    };
  }

  // Get upcoming events
  static async getUpcoming(limit: number = 10): Promise<Event[]> {
    const query = `
      SELECT e.*, 
             u.first_name as creator_first_name,
             u.last_name as creator_last_name
      FROM events e
      LEFT JOIN users u ON e.created_by = u.id
      WHERE e.is_active = TRUE 
      AND (e.start_date >= CURDATE() OR (e.end_date IS NOT NULL AND e.end_date >= CURDATE()))
      ORDER BY e.start_date ASC, e.start_time ASC
      LIMIT ?
    `;

    return executeQuery(query, [limit]);
  }

  // Get events by type
  static async getByType(eventType: string, limit: number = 10): Promise<Event[]> {
    const query = `
      SELECT e.*, 
             u.first_name as creator_first_name,
             u.last_name as creator_last_name
      FROM events e
      LEFT JOIN users u ON e.created_by = u.id
      WHERE e.is_active = TRUE AND e.event_type = ?
      AND (e.start_date >= CURDATE() OR (e.end_date IS NOT NULL AND e.end_date >= CURDATE()))
      ORDER BY e.start_date ASC
      LIMIT ?
    `;

    return executeQuery(query, [eventType, limit]);
  }

  // Get events by date range
  static async getByDateRange(startDate: string, endDate: string): Promise<Event[]> {
    const query = `
      SELECT e.*, 
             u.first_name as creator_first_name,
             u.last_name as creator_last_name
      FROM events e
      LEFT JOIN users u ON e.created_by = u.id
      WHERE e.is_active = TRUE
      AND (
        (e.start_date BETWEEN ? AND ?) OR
        (e.end_date IS NOT NULL AND e.end_date BETWEEN ? AND ?) OR
        (e.start_date <= ? AND (e.end_date IS NULL OR e.end_date >= ?))
      )
      ORDER BY e.start_date ASC, e.start_time ASC
    `;

    return executeQuery(query, [
      startDate, endDate,
      startDate, endDate,
      startDate, endDate
    ]);
  }

  // Update event
  static async update(id: number, createdBy: number, updateData: Partial<CreateEventRequest>): Promise<Event | null> {
    const allowedFields = [
      'title', 'description', 'event_type', 'location', 'latitude', 'longitude',
      'start_date', 'end_date', 'start_time', 'end_time', 'is_recurring',
      'recurrence_pattern', 'entry_fee', 'cultural_significance', 'dress_code',
      'visitor_guidelines', 'contact_info', 'images'
    ];

    const updateFields = Object.keys(updateData)
      .filter(key => allowedFields.includes(key))
      .map(key => `${key} = ?`);

    if (updateFields.length === 0) {
      throw new Error('No valid fields to update');
    }

    const values = Object.keys(updateData)
      .filter(key => allowedFields.includes(key))
      .map(key => {
        const value = updateData[key as keyof CreateEventRequest];
        return (Array.isArray(value) || (typeof value === 'object' && value !== null)) 
          ? JSON.stringify(value) : value;
      });

    const query = `
      UPDATE events 
      SET ${updateFields.join(', ')}, updated_at = CURRENT_TIMESTAMP 
      WHERE id = ? AND created_by = ? AND is_active = TRUE
    `;

    const result = await executeQuery(query, [...values, id, createdBy]);
    
    if (result.affectedRows === 0) {
      return null;
    }

    return this.findById(id);
  }

  // Delete event (soft delete)
  static async delete(id: number, createdBy: number): Promise<boolean> {
    const query = `
      UPDATE events 
      SET is_active = FALSE, updated_at = CURRENT_TIMESTAMP 
      WHERE id = ? AND created_by = ? AND is_active = TRUE
    `;

    const result = await executeQuery(query, [id, createdBy]);
    return result.affectedRows > 0;
  }

  // Get events by creator
  static async getByCreator(createdBy: number, page: number = 1, limit: number = 10): Promise<PaginatedResponse<Event>> {
    const offset = (page - 1) * limit;

    const countQuery = `
      SELECT COUNT(*) as total 
      FROM events 
      WHERE created_by = ? AND is_active = TRUE
    `;

    const dataQuery = `
      SELECT * FROM events 
      WHERE created_by = ? AND is_active = TRUE
      ORDER BY start_date DESC
      LIMIT ? OFFSET ?
    `;

    const [countResult, events] = await Promise.all([
      executeQuery(countQuery, [createdBy]),
      executeQuery(dataQuery, [createdBy, limit, offset])
    ]);

    const total = countResult[0].total;
    const totalPages = Math.ceil(total / limit);

    return {
      data: events,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    };
  }

  // Get nearby events
  static async getNearby(latitude: number, longitude: number, radiusKm: number = 50, limit: number = 10): Promise<Event[]> {
    const query = `
      SELECT e.*, 
             u.first_name as creator_first_name,
             u.last_name as creator_last_name,
             (6371 * acos(cos(radians(?)) * cos(radians(e.latitude)) * 
              cos(radians(e.longitude) - radians(?)) + 
              sin(radians(?)) * sin(radians(e.latitude)))) AS distance
      FROM events e
      LEFT JOIN users u ON e.created_by = u.id
      WHERE e.is_active = TRUE 
      AND e.latitude IS NOT NULL 
      AND e.longitude IS NOT NULL
      AND (e.start_date >= CURDATE() OR (e.end_date IS NOT NULL AND e.end_date >= CURDATE()))
      HAVING distance <= ?
      ORDER BY distance ASC, e.start_date ASC
      LIMIT ?
    `;

    return executeQuery(query, [latitude, longitude, latitude, radiusKm, limit]);
  }

  // Get event statistics
  static async getStats(): Promise<any> {
    const query = `
      SELECT 
        COUNT(*) as total_events,
        COUNT(CASE WHEN is_active = TRUE THEN 1 END) as active_events,
        COUNT(CASE WHEN start_date >= CURDATE() THEN 1 END) as upcoming_events,
        AVG(entry_fee) as average_entry_fee,
        event_type,
        COUNT(*) as type_count
      FROM events
      WHERE is_active = TRUE
      GROUP BY event_type
      WITH ROLLUP
    `;

    return executeQuery(query);
  }

  // Get featured events (free or low-cost cultural events)
  static async getFeatured(limit: number = 6): Promise<Event[]> {
    const query = `
      SELECT e.*, 
             u.first_name as creator_first_name,
             u.last_name as creator_last_name
      FROM events e
      LEFT JOIN users u ON e.created_by = u.id
      WHERE e.is_active = TRUE 
      AND e.entry_fee <= 10
      AND (e.start_date >= CURDATE() OR (e.end_date IS NOT NULL AND e.end_date >= CURDATE()))
      AND e.cultural_significance IS NOT NULL
      ORDER BY e.entry_fee ASC, e.start_date ASC
      LIMIT ?
    `;

    return executeQuery(query, [limit]);
  }
}
