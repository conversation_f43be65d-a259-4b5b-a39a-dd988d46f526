import { executeQuery } from '../config/database';
import { Review, PaginatedResponse } from '../types';
import { TourModel } from './Tour';

export interface CreateReviewRequest {
  booking_id: number;
  rating: number;
  comment?: string;
  guide_rating?: number;
  tour_rating?: number;
  value_rating?: number;
  safety_rating?: number;
  images?: string[];
}

export class ReviewModel {
  // Create a new review
  static async create(userId: number, reviewData: CreateReviewRequest): Promise<Review> {
    const {
      booking_id,
      rating,
      comment,
      guide_rating,
      tour_rating,
      value_rating,
      safety_rating,
      images
    } = reviewData;

    // Get booking details to extract tour_id and guide_id
    const bookingQuery = `
      SELECT b.tour_id, t.guide_id 
      FROM bookings b
      JOIN tours t ON b.tour_id = t.id
      WHERE b.id = ? AND b.user_id = ? AND b.status = 'completed'
    `;
    
    const bookingResult = await executeQuery(bookingQuery, [booking_id, userId]);
    
    if (bookingResult.length === 0) {
      throw new Error('Booking not found or not eligible for review');
    }

    const { tour_id, guide_id } = bookingResult[0];

    // Check if review already exists
    const existingReview = await executeQuery(
      'SELECT id FROM reviews WHERE booking_id = ? AND user_id = ?',
      [booking_id, userId]
    );

    if (existingReview.length > 0) {
      throw new Error('Review already exists for this booking');
    }

    const query = `
      INSERT INTO reviews (
        booking_id, user_id, tour_id, guide_id, rating, comment,
        guide_rating, tour_rating, value_rating, safety_rating, images
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const result = await executeQuery(query, [
      booking_id, userId, tour_id, guide_id, rating, comment,
      guide_rating, tour_rating, value_rating, safety_rating,
      images ? JSON.stringify(images) : null
    ]);

    // Update tour rating
    await TourModel.updateRating(tour_id);

    return this.findById(result.insertId);
  }

  // Find review by ID
  static async findById(id: number): Promise<Review | null> {
    const query = `
      SELECT r.*, 
             u.first_name as user_first_name,
             u.last_name as user_last_name,
             u.profile_image as user_profile_image,
             t.title as tour_title,
             g.first_name as guide_first_name,
             g.last_name as guide_last_name
      FROM reviews r
      LEFT JOIN users u ON r.user_id = u.id
      LEFT JOIN tours t ON r.tour_id = t.id
      LEFT JOIN users g ON r.guide_id = g.id
      WHERE r.id = ?
    `;
    
    const results = await executeQuery(query, [id]);
    return results.length > 0 ? results[0] : null;
  }

  // Get reviews by tour
  static async getByTour(tourId: number, page: number = 1, limit: number = 10): Promise<PaginatedResponse<Review>> {
    const offset = (page - 1) * limit;

    const countQuery = `
      SELECT COUNT(*) as total 
      FROM reviews 
      WHERE tour_id = ? AND is_verified = TRUE
    `;

    const dataQuery = `
      SELECT r.*, 
             u.first_name as user_first_name,
             u.last_name as user_last_name,
             u.profile_image as user_profile_image
      FROM reviews r
      LEFT JOIN users u ON r.user_id = u.id
      WHERE r.tour_id = ? AND r.is_verified = TRUE
      ORDER BY r.created_at DESC
      LIMIT ? OFFSET ?
    `;

    const [countResult, reviews] = await Promise.all([
      executeQuery(countQuery, [tourId]),
      executeQuery(dataQuery, [tourId, limit, offset])
    ]);

    const total = countResult[0].total;
    const totalPages = Math.ceil(total / limit);

    return {
      data: reviews,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    };
  }

  // Get reviews by guide
  static async getByGuide(guideId: number, page: number = 1, limit: number = 10): Promise<PaginatedResponse<Review>> {
    const offset = (page - 1) * limit;

    const countQuery = `
      SELECT COUNT(*) as total 
      FROM reviews 
      WHERE guide_id = ? AND is_verified = TRUE
    `;

    const dataQuery = `
      SELECT r.*, 
             u.first_name as user_first_name,
             u.last_name as user_last_name,
             u.profile_image as user_profile_image,
             t.title as tour_title
      FROM reviews r
      LEFT JOIN users u ON r.user_id = u.id
      LEFT JOIN tours t ON r.tour_id = t.id
      WHERE r.guide_id = ? AND r.is_verified = TRUE
      ORDER BY r.created_at DESC
      LIMIT ? OFFSET ?
    `;

    const [countResult, reviews] = await Promise.all([
      executeQuery(countQuery, [guideId]),
      executeQuery(dataQuery, [guideId, limit, offset])
    ]);

    const total = countResult[0].total;
    const totalPages = Math.ceil(total / limit);

    return {
      data: reviews,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    };
  }

  // Get reviews by user
  static async getByUser(userId: number, page: number = 1, limit: number = 10): Promise<PaginatedResponse<Review>> {
    const offset = (page - 1) * limit;

    const countQuery = `
      SELECT COUNT(*) as total 
      FROM reviews 
      WHERE user_id = ?
    `;

    const dataQuery = `
      SELECT r.*, 
             t.title as tour_title,
             g.first_name as guide_first_name,
             g.last_name as guide_last_name
      FROM reviews r
      LEFT JOIN tours t ON r.tour_id = t.id
      LEFT JOIN users g ON r.guide_id = g.id
      WHERE r.user_id = ?
      ORDER BY r.created_at DESC
      LIMIT ? OFFSET ?
    `;

    const [countResult, reviews] = await Promise.all([
      executeQuery(countQuery, [userId]),
      executeQuery(dataQuery, [userId, limit, offset])
    ]);

    const total = countResult[0].total;
    const totalPages = Math.ceil(total / limit);

    return {
      data: reviews,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    };
  }

  // Update review
  static async update(id: number, userId: number, updateData: Partial<CreateReviewRequest>): Promise<Review | null> {
    const allowedFields = ['rating', 'comment', 'guide_rating', 'tour_rating', 'value_rating', 'safety_rating', 'images'];

    const updateFields = Object.keys(updateData)
      .filter(key => allowedFields.includes(key))
      .map(key => `${key} = ?`);

    if (updateFields.length === 0) {
      throw new Error('No valid fields to update');
    }

    const values = Object.keys(updateData)
      .filter(key => allowedFields.includes(key))
      .map(key => {
        const value = updateData[key as keyof CreateReviewRequest];
        return key === 'images' && Array.isArray(value) ? JSON.stringify(value) : value;
      });

    const query = `
      UPDATE reviews 
      SET ${updateFields.join(', ')}, updated_at = CURRENT_TIMESTAMP 
      WHERE id = ? AND user_id = ?
    `;

    const result = await executeQuery(query, [...values, id, userId]);
    
    if (result.affectedRows === 0) {
      return null;
    }

    // Update tour rating if rating was changed
    if (updateData.rating !== undefined) {
      const review = await this.findById(id);
      if (review) {
        await TourModel.updateRating(review.tour_id);
      }
    }

    return this.findById(id);
  }

  // Delete review
  static async delete(id: number, userId: number): Promise<boolean> {
    // Get tour_id before deletion for rating update
    const review = await this.findById(id);
    
    const query = `
      DELETE FROM reviews 
      WHERE id = ? AND user_id = ?
    `;

    const result = await executeQuery(query, [id, userId]);
    
    if (result.affectedRows > 0 && review) {
      // Update tour rating after deletion
      await TourModel.updateRating(review.tour_id);
      return true;
    }
    
    return false;
  }

  // Moderate review (Admin only)
  static async moderate(id: number, isModerated: boolean, moderationNotes?: string): Promise<Review | null> {
    const query = `
      UPDATE reviews 
      SET is_moderated = ?, moderation_notes = ?, updated_at = CURRENT_TIMESTAMP 
      WHERE id = ?
    `;

    const result = await executeQuery(query, [isModerated, moderationNotes, id]);
    
    if (result.affectedRows === 0) {
      return null;
    }

    return this.findById(id);
  }

  // Add helpful vote
  static async addHelpfulVote(id: number): Promise<boolean> {
    const query = `
      UPDATE reviews 
      SET helpful_votes = helpful_votes + 1 
      WHERE id = ?
    `;

    const result = await executeQuery(query, [id]);
    return result.affectedRows > 0;
  }

  // Get review statistics
  static async getStats(): Promise<any> {
    const query = `
      SELECT 
        COUNT(*) as total_reviews,
        AVG(rating) as average_rating,
        COUNT(CASE WHEN rating = 5 THEN 1 END) as five_star_count,
        COUNT(CASE WHEN rating = 4 THEN 1 END) as four_star_count,
        COUNT(CASE WHEN rating = 3 THEN 1 END) as three_star_count,
        COUNT(CASE WHEN rating = 2 THEN 1 END) as two_star_count,
        COUNT(CASE WHEN rating = 1 THEN 1 END) as one_star_count,
        COUNT(CASE WHEN is_moderated = TRUE THEN 1 END) as moderated_count,
        COUNT(CASE WHEN comment IS NOT NULL AND comment != '' THEN 1 END) as with_comments_count
      FROM reviews
      WHERE is_verified = TRUE
    `;

    const result = await executeQuery(query);
    return result[0];
  }

  // Get recent reviews
  static async getRecent(limit: number = 10): Promise<Review[]> {
    const query = `
      SELECT r.*, 
             u.first_name as user_first_name,
             u.last_name as user_last_name,
             u.profile_image as user_profile_image,
             t.title as tour_title,
             g.first_name as guide_first_name,
             g.last_name as guide_last_name
      FROM reviews r
      LEFT JOIN users u ON r.user_id = u.id
      LEFT JOIN tours t ON r.tour_id = t.id
      LEFT JOIN users g ON r.guide_id = g.id
      WHERE r.is_verified = TRUE
      ORDER BY r.created_at DESC
      LIMIT ?
    `;

    return executeQuery(query, [limit]);
  }

  // Get reviews pending moderation (Admin only)
  static async getPendingModeration(page: number = 1, limit: number = 10): Promise<PaginatedResponse<Review>> {
    const offset = (page - 1) * limit;

    const countQuery = `
      SELECT COUNT(*) as total 
      FROM reviews 
      WHERE is_moderated = FALSE
    `;

    const dataQuery = `
      SELECT r.*, 
             u.first_name as user_first_name,
             u.last_name as user_last_name,
             t.title as tour_title,
             g.first_name as guide_first_name,
             g.last_name as guide_last_name
      FROM reviews r
      LEFT JOIN users u ON r.user_id = u.id
      LEFT JOIN tours t ON r.tour_id = t.id
      LEFT JOIN users g ON r.guide_id = g.id
      WHERE r.is_moderated = FALSE
      ORDER BY r.created_at ASC
      LIMIT ? OFFSET ?
    `;

    const [countResult, reviews] = await Promise.all([
      executeQuery(countQuery),
      executeQuery(dataQuery, [limit, offset])
    ]);

    const total = countResult[0].total;
    const totalPages = Math.ceil(total / limit);

    return {
      data: reviews,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    };
  }
}
