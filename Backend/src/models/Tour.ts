import { executeQuery } from '../config/database';
import { Tour, CreateTourRequest, TourSearchFilters, PaginatedResponse } from '../types';

export class TourModel {
  // Create a new tour
  static async create(guideId: number, tourData: CreateTourRequest): Promise<Tour> {
    const {
      title,
      description,
      duration_hours,
      max_participants,
      price_usd,
      location,
      latitude,
      longitude,
      category,
      difficulty_level = 'easy',
      includes,
      excludes,
      requirements,
      cancellation_policy,
      meeting_point,
      meeting_point_lat,
      meeting_point_lng,
      cultural_significance,
      dress_code
    } = tourData;

    const query = `
      INSERT INTO tours (
        guide_id, title, description, duration_hours, max_participants, 
        price_usd, location, latitude, longitude, category, difficulty_level,
        includes, excludes, requirements, cancellation_policy, meeting_point,
        meeting_point_lat, meeting_point_lng, cultural_significance, dress_code
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const result = await executeQuery(query, [
      guideId, title, description, duration_hours, max_participants,
      price_usd, location, latitude, longitude, category, difficulty_level,
      includes, excludes, requirements, cancellation_policy, meeting_point,
      meeting_point_lat, meeting_point_lng, cultural_significance, dress_code
    ]);

    return this.findById(result.insertId);
  }

  // Find tour by ID with guide information
  static async findById(id: number): Promise<Tour | null> {
    const query = `
      SELECT t.*, 
             u.first_name as guide_first_name, 
             u.last_name as guide_last_name,
             u.profile_image as guide_profile_image,
             u.bio as guide_bio,
             u.languages_spoken as guide_languages,
             u.specializations as guide_specializations
      FROM tours t
      LEFT JOIN users u ON t.guide_id = u.id
      WHERE t.id = ? AND t.is_active = TRUE
    `;
    
    const results = await executeQuery(query, [id]);
    return results.length > 0 ? results[0] : null;
  }

  // Search and filter tours
  static async search(filters: TourSearchFilters): Promise<PaginatedResponse<Tour>> {
    const {
      category,
      location,
      price_min,
      price_max,
      difficulty_level,
      duration_min,
      duration_max,
      rating_min,
      date_from,
      date_to,
      page = 1,
      limit = 10,
      sort_by = 'created_at',
      sort_order = 'desc'
    } = filters;

    const offset = (page - 1) * limit;
    let whereConditions = ['t.is_active = TRUE'];
    let queryParams: any[] = [];

    // Build WHERE conditions
    if (category) {
      whereConditions.push('t.category = ?');
      queryParams.push(category);
    }

    if (location) {
      whereConditions.push('t.location LIKE ?');
      queryParams.push(`%${location}%`);
    }

    if (price_min !== undefined) {
      whereConditions.push('t.price_usd >= ?');
      queryParams.push(price_min);
    }

    if (price_max !== undefined) {
      whereConditions.push('t.price_usd <= ?');
      queryParams.push(price_max);
    }

    if (difficulty_level) {
      whereConditions.push('t.difficulty_level = ?');
      queryParams.push(difficulty_level);
    }

    if (duration_min !== undefined) {
      whereConditions.push('t.duration_hours >= ?');
      queryParams.push(duration_min);
    }

    if (duration_max !== undefined) {
      whereConditions.push('t.duration_hours <= ?');
      queryParams.push(duration_max);
    }

    if (rating_min !== undefined) {
      whereConditions.push('t.average_rating >= ?');
      queryParams.push(rating_min);
    }

    const whereClause = whereConditions.join(' AND ');

    // Validate sort_by to prevent SQL injection
    const allowedSortFields = ['price_usd', 'average_rating', 'duration_hours', 'created_at', 'total_reviews'];
    const sortField = allowedSortFields.includes(sort_by) ? sort_by : 'created_at';
    const sortDirection = sort_order === 'asc' ? 'ASC' : 'DESC';

    // Count query
    const countQuery = `
      SELECT COUNT(*) as total 
      FROM tours t 
      WHERE ${whereClause}
    `;

    // Data query
    const dataQuery = `
      SELECT t.*, 
             u.first_name as guide_first_name, 
             u.last_name as guide_last_name,
             u.profile_image as guide_profile_image
      FROM tours t
      LEFT JOIN users u ON t.guide_id = u.id
      WHERE ${whereClause}
      ORDER BY t.${sortField} ${sortDirection}
      LIMIT ? OFFSET ?
    `;

    const [countResult, tours] = await Promise.all([
      executeQuery(countQuery, queryParams),
      executeQuery(dataQuery, [...queryParams, limit, offset])
    ]);

    const total = countResult[0].total;
    const totalPages = Math.ceil(total / limit);

    return {
      data: tours,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    };
  }

  // Get tours by guide
  static async getByGuide(guideId: number, page: number = 1, limit: number = 10): Promise<PaginatedResponse<Tour>> {
    const offset = (page - 1) * limit;

    const countQuery = `
      SELECT COUNT(*) as total 
      FROM tours 
      WHERE guide_id = ? AND is_active = TRUE
    `;

    const dataQuery = `
      SELECT * FROM tours 
      WHERE guide_id = ? AND is_active = TRUE
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `;

    const [countResult, tours] = await Promise.all([
      executeQuery(countQuery, [guideId]),
      executeQuery(dataQuery, [guideId, limit, offset])
    ]);

    const total = countResult[0].total;
    const totalPages = Math.ceil(total / limit);

    return {
      data: tours,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    };
  }

  // Update tour
  static async update(id: number, guideId: number, updateData: Partial<CreateTourRequest>): Promise<Tour | null> {
    const allowedFields = [
      'title', 'description', 'duration_hours', 'max_participants', 'price_usd',
      'location', 'latitude', 'longitude', 'category', 'difficulty_level',
      'includes', 'excludes', 'requirements', 'cancellation_policy',
      'meeting_point', 'meeting_point_lat', 'meeting_point_lng',
      'cultural_significance', 'dress_code'
    ];

    const updateFields = Object.keys(updateData)
      .filter(key => allowedFields.includes(key))
      .map(key => `${key} = ?`);

    if (updateFields.length === 0) {
      throw new Error('No valid fields to update');
    }

    const values = Object.keys(updateData)
      .filter(key => allowedFields.includes(key))
      .map(key => updateData[key as keyof CreateTourRequest]);

    const query = `
      UPDATE tours 
      SET ${updateFields.join(', ')}, updated_at = CURRENT_TIMESTAMP 
      WHERE id = ? AND guide_id = ? AND is_active = TRUE
    `;

    const result = await executeQuery(query, [...values, id, guideId]);
    
    if (result.affectedRows === 0) {
      return null;
    }

    return this.findById(id);
  }

  // Delete tour (soft delete)
  static async delete(id: number, guideId: number): Promise<boolean> {
    const query = `
      UPDATE tours 
      SET is_active = FALSE, updated_at = CURRENT_TIMESTAMP 
      WHERE id = ? AND guide_id = ? AND is_active = TRUE
    `;

    const result = await executeQuery(query, [id, guideId]);
    return result.affectedRows > 0;
  }

  // Get featured tours
  static async getFeatured(limit: number = 6): Promise<Tour[]> {
    const query = `
      SELECT t.*, 
             u.first_name as guide_first_name, 
             u.last_name as guide_last_name,
             u.profile_image as guide_profile_image
      FROM tours t
      LEFT JOIN users u ON t.guide_id = u.id
      WHERE t.is_active = TRUE AND t.average_rating >= 4.0
      ORDER BY t.average_rating DESC, t.total_reviews DESC
      LIMIT ?
    `;

    return executeQuery(query, [limit]);
  }

  // Get popular tours
  static async getPopular(limit: number = 6): Promise<Tour[]> {
    const query = `
      SELECT t.*, 
             u.first_name as guide_first_name, 
             u.last_name as guide_last_name,
             u.profile_image as guide_profile_image
      FROM tours t
      LEFT JOIN users u ON t.guide_id = u.id
      WHERE t.is_active = TRUE
      ORDER BY t.total_bookings DESC, t.average_rating DESC
      LIMIT ?
    `;

    return executeQuery(query, [limit]);
  }

  // Update tour rating
  static async updateRating(tourId: number): Promise<void> {
    const query = `
      UPDATE tours 
      SET 
        average_rating = (
          SELECT COALESCE(AVG(rating), 0) 
          FROM reviews 
          WHERE tour_id = ? AND is_verified = TRUE
        ),
        total_reviews = (
          SELECT COUNT(*) 
          FROM reviews 
          WHERE tour_id = ? AND is_verified = TRUE
        )
      WHERE id = ?
    `;

    await executeQuery(query, [tourId, tourId, tourId]);
  }

  // Get tour statistics
  static async getStats(): Promise<any> {
    const query = `
      SELECT 
        COUNT(*) as total_tours,
        COUNT(CASE WHEN is_active = TRUE THEN 1 END) as active_tours,
        AVG(price_usd) as average_price,
        AVG(average_rating) as average_rating,
        SUM(total_bookings) as total_bookings,
        category,
        COUNT(*) as category_count
      FROM tours
      GROUP BY category
      WITH ROLLUP
    `;

    return executeQuery(query);
  }

  // Check tour availability for booking
  static async checkAvailability(tourId: number, date: string): Promise<boolean> {
    const query = `
      SELECT t.max_participants,
             COALESCE(SUM(b.participants), 0) as booked_participants
      FROM tours t
      LEFT JOIN bookings b ON t.id = b.tour_id 
        AND b.booking_date = ? 
        AND b.status IN ('confirmed', 'pending')
      WHERE t.id = ? AND t.is_active = TRUE
      GROUP BY t.id, t.max_participants
    `;

    const result = await executeQuery(query, [date, tourId]);
    
    if (result.length === 0) {
      return false;
    }

    const { max_participants, booked_participants } = result[0];
    return booked_participants < max_participants;
  }
}
