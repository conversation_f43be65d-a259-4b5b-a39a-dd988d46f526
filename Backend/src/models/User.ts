import { executeQuery } from '../config/database';
import { User, CreateUserRequest } from '../types';
import bcrypt from 'bcrypt';

export class UserModel {
  // Create a new user
  static async create(userData: CreateUserRequest): Promise<User> {
    const {
      email,
      password,
      first_name,
      last_name,
      phone,
      user_type = 'tourist',
      preferred_language = 'en'
    } = userData;

    // Hash password
    const saltRounds = parseInt(process.env.BCRYPT_ROUNDS || '12');
    const password_hash = await bcrypt.hash(password, saltRounds);

    const query = `
      INSERT INTO users (
        email, password_hash, first_name, last_name, phone, 
        user_type, preferred_language
      ) VALUES (?, ?, ?, ?, ?, ?, ?)
    `;

    const result = await executeQuery(query, [
      email, password_hash, first_name, last_name, phone,
      user_type, preferred_language
    ]);

    return this.findById(result.insertId);
  }

  // Find user by ID
  static async findById(id: number): Promise<User | null> {
    const query = 'SELECT * FROM users WHERE id = ? AND is_active = TRUE';
    const results = await executeQuery(query, [id]);
    
    return results.length > 0 ? results[0] : null;
  }

  // Find user by email
  static async findByEmail(email: string): Promise<User | null> {
    const query = 'SELECT * FROM users WHERE email = ? AND is_active = TRUE';
    const results = await executeQuery(query, [email]);
    
    return results.length > 0 ? results[0] : null;
  }

  // Verify password
  static async verifyPassword(plainPassword: string, hashedPassword: string): Promise<boolean> {
    return bcrypt.compare(plainPassword, hashedPassword);
  }

  // Update user profile
  static async updateProfile(id: number, updateData: Partial<User>): Promise<User | null> {
    const allowedFields = [
      'first_name', 'last_name', 'phone', 'profile_image', 
      'preferred_language', 'bio', 'emergency_contact_name', 
      'emergency_contact_phone', 'specializations', 'languages_spoken'
    ];

    const updateFields = Object.keys(updateData)
      .filter(key => allowedFields.includes(key))
      .map(key => `${key} = ?`);

    if (updateFields.length === 0) {
      throw new Error('No valid fields to update');
    }

    const values = Object.keys(updateData)
      .filter(key => allowedFields.includes(key))
      .map(key => {
        const value = updateData[key as keyof User];
        return Array.isArray(value) ? JSON.stringify(value) : value;
      });

    const query = `
      UPDATE users 
      SET ${updateFields.join(', ')}, updated_at = CURRENT_TIMESTAMP 
      WHERE id = ? AND is_active = TRUE
    `;

    await executeQuery(query, [...values, id]);
    return this.findById(id);
  }

  // Update password
  static async updatePassword(id: number, newPassword: string): Promise<boolean> {
    const saltRounds = parseInt(process.env.BCRYPT_ROUNDS || '12');
    const password_hash = await bcrypt.hash(newPassword, saltRounds);

    const query = `
      UPDATE users 
      SET password_hash = ?, updated_at = CURRENT_TIMESTAMP 
      WHERE id = ? AND is_active = TRUE
    `;

    const result = await executeQuery(query, [password_hash, id]);
    return result.affectedRows > 0;
  }

  // Verify user account
  static async verifyAccount(id: number): Promise<boolean> {
    const query = `
      UPDATE users 
      SET is_verified = TRUE, updated_at = CURRENT_TIMESTAMP 
      WHERE id = ? AND is_active = TRUE
    `;

    const result = await executeQuery(query, [id]);
    return result.affectedRows > 0;
  }

  // Deactivate user account
  static async deactivateAccount(id: number): Promise<boolean> {
    const query = `
      UPDATE users 
      SET is_active = FALSE, updated_at = CURRENT_TIMESTAMP 
      WHERE id = ?
    `;

    const result = await executeQuery(query, [id]);
    return result.affectedRows > 0;
  }

  // Get all guides
  static async getGuides(page: number = 1, limit: number = 10): Promise<{ guides: User[], total: number }> {
    const offset = (page - 1) * limit;

    const countQuery = `
      SELECT COUNT(*) as total 
      FROM users 
      WHERE user_type = 'guide' AND is_active = TRUE AND is_verified = TRUE
    `;

    const dataQuery = `
      SELECT id, email, first_name, last_name, phone, profile_image, 
             bio, specializations, languages_spoken, created_at
      FROM users 
      WHERE user_type = 'guide' AND is_active = TRUE AND is_verified = TRUE
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `;

    const [countResult, guides] = await Promise.all([
      executeQuery(countQuery),
      executeQuery(dataQuery, [limit, offset])
    ]);

    return {
      guides,
      total: countResult[0].total
    };
  }

  // Search users by role and criteria
  static async searchUsers(
    role?: string,
    searchTerm?: string,
    page: number = 1,
    limit: number = 10
  ): Promise<{ users: User[], total: number }> {
    const offset = (page - 1) * limit;
    let whereConditions = ['is_active = TRUE'];
    let queryParams: any[] = [];

    if (role) {
      whereConditions.push('user_type = ?');
      queryParams.push(role);
    }

    if (searchTerm) {
      whereConditions.push('(first_name LIKE ? OR last_name LIKE ? OR email LIKE ?)');
      const searchPattern = `%${searchTerm}%`;
      queryParams.push(searchPattern, searchPattern, searchPattern);
    }

    const whereClause = whereConditions.join(' AND ');

    const countQuery = `
      SELECT COUNT(*) as total 
      FROM users 
      WHERE ${whereClause}
    `;

    const dataQuery = `
      SELECT id, email, first_name, last_name, phone, profile_image, 
             user_type, preferred_language, is_verified, created_at
      FROM users 
      WHERE ${whereClause}
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `;

    const [countResult, users] = await Promise.all([
      executeQuery(countQuery, queryParams),
      executeQuery(dataQuery, [...queryParams, limit, offset])
    ]);

    return {
      users,
      total: countResult[0].total
    };
  }

  // Get user statistics
  static async getUserStats(): Promise<any> {
    const query = `
      SELECT 
        user_type,
        COUNT(*) as count,
        SUM(CASE WHEN is_verified = TRUE THEN 1 ELSE 0 END) as verified_count
      FROM users 
      WHERE is_active = TRUE
      GROUP BY user_type
    `;

    const results = await executeQuery(query);
    
    const stats = {
      total: 0,
      tourists: 0,
      guides: 0,
      servers: 0,
      admins: 0,
      verified: 0
    };

    results.forEach((row: any) => {
      stats.total += row.count;
      stats.verified += row.verified_count;
      
      switch (row.user_type) {
        case 'tourist':
          stats.tourists = row.count;
          break;
        case 'guide':
          stats.guides = row.count;
          break;
        case 'server':
          stats.servers = row.count;
          break;
        case 'admin':
          stats.admins = row.count;
          break;
      }
    });

    return stats;
  }

  // Check if email exists
  static async emailExists(email: string, excludeId?: number): Promise<boolean> {
    let query = 'SELECT id FROM users WHERE email = ?';
    let params: any[] = [email];

    if (excludeId) {
      query += ' AND id != ?';
      params.push(excludeId);
    }

    const results = await executeQuery(query, params);
    return results.length > 0;
  }
}
