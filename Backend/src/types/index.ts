// User Types
export interface User {
  id: number;
  email: string;
  password_hash: string;
  first_name: string;
  last_name: string;
  phone?: string;
  profile_image?: string;
  user_type: 'tourist' | 'guide' | 'admin' | 'server';
  preferred_language: 'en' | 'km' | 'zh';
  is_verified: boolean;
  is_active: boolean;
  emergency_contact_name?: string;
  emergency_contact_phone?: string;
  bio?: string;
  specializations?: string[];
  languages_spoken?: string[];
  verification_documents?: any;
  created_at: Date;
  updated_at: Date;
}

export interface CreateUserRequest {
  email: string;
  password: string;
  first_name: string;
  last_name: string;
  phone?: string;
  user_type?: 'tourist' | 'guide' | 'admin' | 'server';
  preferred_language?: 'en' | 'km' | 'zh';
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
}

// Tour Types
export interface Tour {
  id: number;
  guide_id: number;
  title: string;
  description: string;
  duration_hours: number;
  max_participants: number;
  price_usd: number;
  location: string;
  latitude?: number;
  longitude?: number;
  category: 'cultural' | 'adventure' | 'nature' | 'food' | 'historical' | 'religious' | 'shopping';
  difficulty_level: 'easy' | 'moderate' | 'challenging';
  includes?: string;
  excludes?: string;
  requirements?: string;
  cancellation_policy?: string;
  meeting_point?: string;
  meeting_point_lat?: number;
  meeting_point_lng?: number;
  is_active: boolean;
  average_rating: number;
  total_reviews: number;
  total_bookings: number;
  seasonal_availability?: any;
  cultural_significance?: string;
  dress_code?: string;
  created_at: Date;
  updated_at: Date;
}

export interface CreateTourRequest {
  title: string;
  description: string;
  duration_hours: number;
  max_participants: number;
  price_usd: number;
  location: string;
  latitude?: number;
  longitude?: number;
  category: 'cultural' | 'adventure' | 'nature' | 'food' | 'historical' | 'religious' | 'shopping';
  difficulty_level?: 'easy' | 'moderate' | 'challenging';
  includes?: string;
  excludes?: string;
  requirements?: string;
  cancellation_policy?: string;
  meeting_point?: string;
  meeting_point_lat?: number;
  meeting_point_lng?: number;
  cultural_significance?: string;
  dress_code?: string;
}

// Booking Types
export interface Booking {
  id: number;
  user_id: number;
  tour_id: number;
  booking_date: Date;
  booking_time?: string;
  participants: number;
  total_amount: number;
  currency: string;
  status: 'pending' | 'confirmed' | 'cancelled' | 'completed' | 'refunded';
  payment_status: 'pending' | 'paid' | 'failed' | 'refunded';
  payment_method: 'stripe' | 'paypal' | 'bakong';
  payment_id?: string;
  payment_intent_id?: string;
  special_requests?: string;
  emergency_contact_name?: string;
  emergency_contact_phone?: string;
  cancellation_reason?: string;
  refund_amount: number;
  guide_notes?: string;
  tourist_notes?: string;
  created_at: Date;
  updated_at: Date;
}

export interface CreateBookingRequest {
  tour_id: number;
  booking_date: string;
  booking_time?: string;
  participants: number;
  special_requests?: string;
  emergency_contact_name?: string;
  emergency_contact_phone?: string;
}

// Hotel Types
export interface Hotel {
  id: number;
  owner_id?: number;
  name: string;
  description?: string;
  address: string;
  latitude?: number;
  longitude?: number;
  star_rating?: number;
  price_per_night: number;
  amenities?: string[];
  room_types?: any[];
  images?: string[];
  contact_phone?: string;
  contact_email?: string;
  check_in_time: string;
  check_out_time: string;
  cancellation_policy?: string;
  is_active: boolean;
  average_rating: number;
  total_reviews: number;
  total_bookings: number;
  created_at: Date;
  updated_at: Date;
}

// Transportation Types
export interface Transportation {
  id: number;
  provider_id?: number;
  vehicle_type: 'car' | 'van' | 'bus' | 'motorbike' | 'tuk_tuk' | 'boat';
  vehicle_name: string;
  description?: string;
  capacity: number;
  price_per_km?: number;
  base_price?: number;
  hourly_rate?: number;
  driver_name?: string;
  driver_phone?: string;
  driver_license?: string;
  vehicle_license?: string;
  images?: string[];
  amenities?: string[];
  is_active: boolean;
  average_rating: number;
  total_reviews: number;
  total_bookings: number;
  current_location_lat?: number;
  current_location_lng?: number;
  last_location_update?: Date;
  created_at: Date;
  updated_at: Date;
}

// Event Types
export interface Event {
  id: number;
  title: string;
  description: string;
  event_type: 'festival' | 'market' | 'ceremony' | 'performance' | 'workshop' | 'exhibition';
  location: string;
  latitude?: number;
  longitude?: number;
  start_date: Date;
  end_date?: Date;
  start_time?: string;
  end_time?: string;
  is_recurring: boolean;
  recurrence_pattern?: any;
  entry_fee: number;
  cultural_significance?: string;
  dress_code?: string;
  visitor_guidelines?: string;
  contact_info?: any;
  images?: string[];
  is_active: boolean;
  created_by?: number;
  created_at: Date;
  updated_at: Date;
}

// Review Types
export interface Review {
  id: number;
  booking_id: number;
  user_id: number;
  tour_id: number;
  guide_id: number;
  rating: number;
  comment?: string;
  guide_rating?: number;
  tour_rating?: number;
  value_rating?: number;
  safety_rating?: number;
  images?: string[];
  is_verified: boolean;
  is_moderated: boolean;
  moderation_notes?: string;
  helpful_votes: number;
  created_at: Date;
  updated_at: Date;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  timestamp: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Search and Filter Types
export interface TourSearchFilters {
  category?: string;
  location?: string;
  price_min?: number;
  price_max?: number;
  difficulty_level?: string;
  duration_min?: number;
  duration_max?: number;
  rating_min?: number;
  date_from?: string;
  date_to?: string;
  page?: number;
  limit?: number;
  sort_by?: 'price' | 'rating' | 'duration' | 'created_at';
  sort_order?: 'asc' | 'desc';
}

// JWT Payload Type
export interface JwtPayload {
  id: number;
  email: string;
  user_type: string;
  iat?: number;
  exp?: number;
}

// Express Request Extension
declare global {
  namespace Express {
    interface Request {
      user?: JwtPayload;
    }
  }
}
