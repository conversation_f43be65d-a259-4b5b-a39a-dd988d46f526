{"configVersion": 2, "packages": [{"name": "async", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "boolean_selector", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "characters", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/characters-1.3.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "clock", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "collection", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "cupertino_icons", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "fake_async", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter", "rootUri": "file:///home/<USER>/flutter/packages/flutter", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "flutter_lints", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-4.0.0", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "flutter_test", "rootUri": "file:///home/<USER>/flutter/packages/flutter_test", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "leak_tracker", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.5", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "leak_tracker_flutter_testing", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.5", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "leak_tracker_testing", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "lints", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/lints-4.0.0", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "matcher", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.16+1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "material_color_utilities", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "meta", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/meta-1.15.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "path", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/path-1.9.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "sky_engine", "rootUri": "file:///home/<USER>/flutter/bin/cache/pkg/sky_engine", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "source_span", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "stack_trace", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.11.1", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "stream_channel", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.2", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "string_scanner", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.2.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "term_glyph", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "test_api", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.2", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "vector_math", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "vm_service", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/vm_service-14.2.5", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "mobile_app", "rootUri": "../", "packageUri": "lib/", "languageVersion": "3.5"}], "generated": "2025-08-20T07:39:43.870984Z", "generator": "pub", "generatorVersion": "3.5.3", "flutterRoot": "file:///home/<USER>/flutter", "flutterVersion": "3.24.3", "pubCache": "file:///home/<USER>/.pub-cache"}